# Legal Hebrew Nexus - מערכת ניהול משרד עורכי דין

## Project Overview

A comprehensive legal office management system built for Israeli law firms. Features include client management, case tracking, time logging, WhatsApp integration, and automated workflows.

## Technologies Used

This project is built with:

- **Frontend:** React 18, TypeScript, Vite
- **UI Framework:** shadcn/ui, Tailwind CSS
- **Backend:** Supabase (Database, Auth, Edge Functions)
- **Integrations:** WhatsApp (GreenAPI), Twilio Voice
- **Deployment:** Vercel

## Development Setup

### Prerequisites
- Node.js 18+ and npm
- Git

### Local Development

```sh
# Clone the repository
git clone <YOUR_GIT_URL>

# Navigate to project directory
cd legal-hebrew-nexus

# Install dependencies
npm install

# Start development server
npm run dev
```

### Available Scripts

```sh
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
```

## Features

### Core Functionality
- **Multi-tenant Architecture** - Support for multiple law firms
- **User Management** - Role-based access (Super Admin, Company Admin, Users)
- **Client & Lead Management** - Comprehensive CRM for legal clients
- **Case Management** - Track cases with status updates and assignments
- **Time Tracking** - Manual and timer-based time logging
- **Dashboard & Reporting** - Analytics and KPIs for business insights

### Integrations
- **WhatsApp Business** - Client communication via GreenAPI
- **Voice Calls** - Twilio integration for client calls
- **Marketing Automation** - Workflow-based client engagement
- **Real-time Updates** - Live data synchronization

## Deployment

### Vercel (Recommended)

```sh
# Install Vercel CLI
npm install -g vercel

# Deploy
vercel

# Or connect GitHub repo at vercel.com
```

### Build Configuration
- **Build Command:** `npm run build`
- **Output Directory:** `dist`
- **Framework:** Vite

## Environment Setup

The application uses Supabase for backend services. Configuration is handled in:
- `src/integrations/supabase/client.ts` - Database connection
- `supabase/config.toml` - Edge functions configuration

## Architecture

### Frontend
- React 18 with TypeScript
- Vite for build tooling
- React Query for state management
- React Router for navigation
- shadcn/ui component library

### Backend
- Supabase PostgreSQL database
- Row Level Security (RLS) for multi-tenancy
- Edge Functions for business logic
- Real-time subscriptions

### Security
- JWT-based authentication
- Role-based access control
- API key authentication for integrations
- HTTPS enforcement

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
