import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useCompany } from '@/contexts/CompanyContext';
import { useToast } from './use-toast';

export interface CaseDocument {
  id: string;
  case_id: string;
  filename: string;
  file_path: string;
  file_size?: number;
  file_type?: string;
  user_id: string;
  company_id: string;
  uploaded_at: string;
}

export const useCaseDocuments = (caseId?: string) => {
  const [documents, setDocuments] = useState<CaseDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const { user, userRole } = useAuth();
  const { currentCompany } = useCompany();
  const { toast } = useToast();

  const fetchDocuments = async () => {
    if (!user || !caseId) return;

    try {
      const { data, error } = await supabase
        .from('case_documents')
        .select('*')
        .eq('case_id', caseId)
        .order('uploaded_at', { ascending: false });

      if (error) throw error;
      setDocuments(data || []);
    } catch (error) {
      console.error('Error fetching documents:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה בטעינת המסמכים",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const uploadDocument = async (file: File) => {
    if (!user || !caseId || !currentCompany?.id) return;

    setUploading(true);
    try {
      // Create unique file path
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}/${caseId}/${Date.now()}.${fileExt}`;
      
      // Upload file to storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('case-documents')
        .upload(fileName, file);

      if (uploadError) throw uploadError;

      // Save document metadata to database
      const { data, error } = await supabase
        .from('case_documents')
        .insert({
          case_id: caseId,
          filename: file.name,
          file_path: uploadData.path,
          file_size: file.size,
          file_type: file.type,
          user_id: user.id,
          company_id: currentCompany.id,
        })
        .select()
        .single();

      if (error) throw error;

      setDocuments(prev => [data, ...prev]);
      toast({
        title: "הצלחה",
        description: "המסמך הועלה בהצלחה",
      });

      return data;
    } catch (error) {
      console.error('Error uploading document:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה בהעלאת המסמך",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const downloadDocument = async (document: CaseDocument) => {
    try {
      const { data, error } = await supabase.storage
        .from('case-documents')
        .download(document.file_path);

      if (error) throw error;

      // Create download link
      const url = URL.createObjectURL(data);
      const a = window.document.createElement('a');
      a.href = url;
      a.download = document.filename;
      window.document.body.appendChild(a);
      a.click();
      window.document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "הצלחה",
        description: "המסמך הורד בהצלחה",
      });
    } catch (error) {
      console.error('Error downloading document:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה בהורדת המסמך",
        variant: "destructive",
      });
    }
  };

  const deleteDocument = async (documentId: string, filePath: string) => {
    try {
      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from('case-documents')
        .remove([filePath]);

      if (storageError) throw storageError;

      // Delete from database
      const { error: dbError } = await supabase
        .from('case_documents')
        .delete()
        .eq('id', documentId);

      if (dbError) throw dbError;

      setDocuments(prev => prev.filter(d => d.id !== documentId));
      toast({
        title: "הצלחה",
        description: "המסמך נמחק בהצלחה",
      });
    } catch (error) {
      console.error('Error deleting document:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה במחיקת המסמך",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (user && caseId) {
      fetchDocuments();
    }
  }, [user, caseId]);

  return {
    documents,
    isLoading,
    uploading,
    uploadDocument,
    downloadDocument,
    deleteDocument,
    refetchDocuments: fetchDocuments,
  };
};