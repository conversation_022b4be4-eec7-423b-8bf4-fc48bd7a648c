import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useToast } from './use-toast';
import { useCompany } from '@/contexts/CompanyContext';

export interface Case {
  id: string;
  title: string;
  description?: string;
  case_type_id?: string;
  status: string;
  value?: number;
  lead_id?: string;
  user_id: string;
  company_id: string;
  deadline?: string;
  total_time_logged: number;
  created_at: string;
  updated_at: string;
  case_type?: {
    id: string;
    name: string;
    hourly_rate: number;
  };
  lead?: {
    id: string;
    full_name: string;
    phone: string;
    email?: string;
  };
}

export interface CaseType {
  id: string;
  name: string;
  hourly_rate: number;
  company_id: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export const useCases = () => {
  const [cases, setCases] = useState<Case[]>([]);
  const [caseTypes, setCaseTypes] = useState<CaseType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();
  const { currentCompany } = useCompany();
  const { toast } = useToast();
  const hasFetched = useRef(false);
  const requestCache = useRef(new Map());

  const fetchCases = useCallback(async () => {
    if (!currentCompany?.id) return;

    const cacheKey = `cases-${currentCompany.id}`;
    if (requestCache.current.has(cacheKey)) {
      return requestCache.current.get(cacheKey);
    }

    try {
      const promise = supabase
        .from('cases')
        .select(`
          *,
          case_type:case_types(id, name, hourly_rate),
          lead:leads(id, full_name, phone, email)
        `)
        .eq('company_id', currentCompany.id)
        .order('created_at', { ascending: false });

      requestCache.current.set(cacheKey, promise);

      const { data, error } = await promise;

      if (error) throw error;
      setCases(data || []);

      // Clear cache after 30 seconds
      setTimeout(() => requestCache.current.delete(cacheKey), 30000);
    } catch (error) {
      console.error('Error fetching cases:', error);
      requestCache.current.delete(cacheKey);
      toast({
        title: "שגיאה",
        description: "שגיאה בטעינת התיקים",
        variant: "destructive",
      });
    }
  }, [currentCompany?.id, toast]);

  const fetchCaseTypes = useCallback(async () => {
    if (!currentCompany?.id) return;

    try {
      const { data, error } = await supabase
        .from('case_types')
        .select('*')
        .eq('company_id', currentCompany.id)
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      setCaseTypes(data || []);
    } catch (error) {
      console.error('Error fetching case types:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה בטעינת סוגי התיקים",
        variant: "destructive",
      });
    }
  }, [currentCompany?.id, toast]);

  const addCase = async (caseData: {
    title: string;
    description?: string;
    case_type_id?: string;
    status: string;
    value?: number;
    lead_id?: string;
    deadline?: string;
    assigned_user_id?: string;
  }) => {
    if (!user || !currentCompany?.id) return;

    try {
      const { data, error } = await supabase
        .from('cases')
        .insert({
          ...caseData,
          user_id: user.id,
          company_id: currentCompany.id,
        })
        .select(`
          *,
          case_type:case_types(id, name, hourly_rate),
          lead:leads(id, full_name, phone, email)
        `)
        .single();

      if (error) throw error;

      setCases(prev => [data, ...prev]);
      toast({
        title: "הצלחה",
        description: "התיק נוצר בהצלחה",
      });

      return data;
    } catch (error) {
      console.error('Error creating case:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה ביצירת התיק",
        variant: "destructive",
      });
    }
  };

  const updateCase = async (caseId: string, updates: Partial<Case>) => {
    try {
      const { data, error } = await supabase
        .from('cases')
        .update(updates)
        .eq('id', caseId)
        .select(`
          *,
          case_type:case_types(id, name, hourly_rate),
          lead:leads(id, full_name, phone, email)
        `)
        .single();

      if (error) throw error;

      setCases(prev => prev.map(c => c.id === caseId ? data : c));
      toast({
        title: "הצלחה",
        description: "התיק עודכן בהצלחה",
      });

      return data;
    } catch (error) {
      console.error('Error updating case:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה בעדכון התיק",
        variant: "destructive",
      });
    }
  };

  const deleteCase = async (caseId: string) => {
    try {
      const { error } = await supabase
        .from('cases')
        .delete()
        .eq('id', caseId);

      if (error) throw error;

      setCases(prev => prev.filter(c => c.id !== caseId));
      toast({
        title: "הצלחה",
        description: "התיק נמחק בהצלחה",
      });
    } catch (error) {
      console.error('Error deleting case:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה במחיקת התיק",
        variant: "destructive",
      });
    }
  };

  const getCaseById = useCallback(async (caseId: string) => {
    try {
      const { data, error } = await supabase
        .from('cases')
        .select(`
          *,
          case_type:case_types(id, name, hourly_rate),
          lead:leads(id, full_name, phone, email)
        `)
        .eq('id', caseId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching case:', error);
      return null;
    }
  }, []);

  const addCaseType = async (typeData: {
    name: string;
    hourly_rate: number;
    is_active?: boolean;
  }) => {
    if (!user || !currentCompany?.id) return;

    try {
      const { data, error } = await supabase
        .from('case_types')
        .insert({
          ...typeData,
          company_id: currentCompany.id,
        })
        .select()
        .single();

      if (error) throw error;

      setCaseTypes(prev => [...prev, data]);
      toast({
        title: "הצלחה",
        description: "סוג התיק נוצר בהצלחה",
      });

      return data;
    } catch (error) {
      console.error('Error creating case type:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה ביצירת סוג התיק",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (currentCompany?.id && !hasFetched.current) {
      hasFetched.current = true;
      Promise.all([
        fetchCases(),
        fetchCaseTypes()
      ]).finally(() => setIsLoading(false));
    }

    // Cleanup function to prevent memory leaks
    return () => {
      requestCache.current.clear();
      hasFetched.current = false;
    };
  }, [currentCompany?.id, fetchCases, fetchCaseTypes]);

  return {
    cases,
    caseTypes,
    isLoading,
    addCase,
    updateCase,
    deleteCase,
    getCaseById,
    addCaseType,
    refetchCases: fetchCases,
    refetchCaseTypes: fetchCaseTypes,
  };
};