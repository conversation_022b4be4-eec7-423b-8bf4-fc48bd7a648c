import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useCompany } from '@/contexts/CompanyContext';
import { DashboardFilters } from './useDashboardData';

export interface OptimizedDashboardMetrics {
  leads: {
    total: number;
    closed: number;
    close_rate: number;
    total_value: number;
    closed_deals_revenue: number;
    by_status: Array<{
      status: string;
      count: number;
      value: number;
    }>;
  };
  cases: {
    total: number;
    active: number;
    completed: number;
    by_type: Array<{
      type: string;
      count: number;
      value: number;
    }>;
    by_status: Array<{
      status: string;
      count: number;
    }>;
  };
  time_entries: {
    total_hours: number;
    billable_hours: number;
    total_cost: number;
    average_hourly_rate: number;
    utilization_rate: number;
  };
  revenue_by_month: Array<{
    month: string;
    revenue: number;
    hours: number;
    closed_deals: number;
  }>;
}

/**
 * Optimized dashboard hook using single database function call
 * Replaces the old 3-query pattern with a single optimized query
 * 
 * Performance improvements:
 * - 3 queries → 1 query (70% reduction)
 * - Server-side aggregation (faster processing)
 * - Proper caching with React Query
 * - Automatic background refetch
 */
export const useOptimizedDashboard = (filters: DashboardFilters) => {
  const { currentCompany } = useCompany();
  const queryClient = useQueryClient();

  const query = useQuery({
    queryKey: [
      'optimized-dashboard', 
      currentCompany?.id, 
      filters.dateRange.from.toISOString(),
      filters.dateRange.to.toISOString(),
      filters.caseTypeId
    ],
    queryFn: async (): Promise<OptimizedDashboardMetrics> => {
      if (!currentCompany?.id) {
        console.warn('⚠️ No company selected for dashboard metrics');
        throw new Error('No company selected');
      }

      const startTime = performance.now();

      try {
        const { data, error } = await supabase.rpc('get_dashboard_metrics', {
          p_company_id: currentCompany.id,
          p_date_from: filters.dateRange.from.toISOString(),
          p_date_to: filters.dateRange.to.toISOString()
        });

        if (error) {
          // Provide more specific error messages
          if (error.message?.includes('company')) {
            throw new Error(`Company data error: ${error.message}`);
          }
          throw new Error(`Database error: ${error.message || 'Unknown error'}`);
        }

        if (!data) {
          throw new Error('No data returned from server');
        }

        // Transform the data to match the expected interface
        const metrics: OptimizedDashboardMetrics = {
          leads: {
            total: data.leads?.total || 0,
            closed: data.leads?.closed || 0,
            close_rate: data.leads?.close_rate || 0,
            total_value: data.leads?.total_value || 0,
            closed_deals_revenue: data.leads?.closed_deals_revenue || 0,
            by_status: data.leads?.by_status || []
          },
          cases: {
            total: data.cases?.total || 0,
            active: data.cases?.active || 0,
            completed: data.cases?.completed || 0,
            by_type: data.cases?.by_type || [],
            by_status: data.cases?.by_status || []
          },
          time_entries: {
            total_hours: data.time_entries?.total_hours || 0,
            billable_hours: data.time_entries?.billable_hours || 0,
            total_cost: data.time_entries?.total_cost || 0,
            average_hourly_rate: data.time_entries?.average_hourly_rate || 0,
            utilization_rate: data.time_entries?.utilization_rate || 0
          },
          revenue_by_month: data.revenue_by_month || []
        };

        return metrics;
      } catch (error) {
        // Re-throw with more context
        if (error instanceof Error) {
          throw new Error(`Dashboard metrics failed: ${error.message}`);
        }
        throw new Error('Dashboard metrics failed: Unknown error');
      }
    },
    enabled: !!currentCompany?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes - longer stale time for better performance
    gcTime: 10 * 60 * 1000, // 10 minutes - longer garbage collection time
    refetchOnWindowFocus: false, // Don't refetch on window focus
    refetchOnMount: false, // Don't refetch on mount if data exists
    retry: 2, // Reduce retry attempts
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000), // Faster backoff

    // Reduce background refetch frequency
    refetchInterval: 10 * 60 * 1000, // Every 10 minutes instead of 5

    // Only refetch in background if window is visible
    refetchIntervalInBackground: false,

    // Don't keep previous data when company changes - show loading instead
    placeholderData: undefined, // Don't show old data when company changes
    
    // Error handling
    onError: (error) => {
      console.error('Dashboard metrics query failed:', error);
    },
    
    // Success logging
    onSuccess: (data) => {
      console.log('📊 Dashboard metrics loaded successfully:', {
        leads: data.leads.total,
        cases: data.cases.total,
        hours: data.time_entries.total_hours.toFixed(1)
      });
    }
  });

  // Prefetch next period data for better UX
  const prefetchNextPeriod = () => {
    const nextFrom = new Date(filters.dateRange.to);
    const nextTo = new Date(filters.dateRange.to);
    nextTo.setMonth(nextTo.getMonth() + 1);

    queryClient.prefetchQuery({
      queryKey: [
        'optimized-dashboard', 
        currentCompany?.id, 
        nextFrom.toISOString(),
        nextTo.toISOString(),
        filters.caseTypeId
      ],
      queryFn: async () => {
        const { data } = await supabase.rpc('get_dashboard_metrics', {
          p_company_id: currentCompany!.id,
          p_date_from: nextFrom.toISOString(),
          p_date_to: nextTo.toISOString()
        });
        return data;
      },
      staleTime: 5 * 60 * 1000
    });
  };

  // Invalidate cache when company changes
  const invalidateCache = () => {
    queryClient.invalidateQueries({
      queryKey: ['optimized-dashboard', currentCompany?.id]
    });
  };

  // Manual refresh function
  const refresh = () => {
    return queryClient.invalidateQueries({
      queryKey: ['optimized-dashboard', currentCompany?.id]
    });
  };



  return {
    data: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    isFetching: query.isFetching,
    isStale: query.isStale,
    
    // Additional utilities
    prefetchNextPeriod,
    invalidateCache,
    refresh,
    
    // Performance metrics
    queryKey: query.queryKey,
    dataUpdatedAt: query.dataUpdatedAt,
    
    // Backward compatibility with old hook
    metrics: query.data,
    refetch: query.refetch
  };
};

/**
 * Hook for super admin company statistics
 * Optimized for super admin dashboard
 */
export const useCompanyStatistics = () => {
  return useQuery({
    queryKey: ['company-statistics'],
    queryFn: async () => {
      console.log('📈 Fetching company statistics...');
      
      const { data, error } = await supabase.rpc('get_company_statistics');
      
      if (error) {
        console.error('❌ Company statistics error:', error);
        throw error;
      }
      
      return data;
    },
    staleTime: 15 * 60 * 1000, // 15 minutes - admin data changes less frequently
    cacheTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false,
    refetchInterval: 15 * 60 * 1000, // Refresh every 15 minutes
    retry: 2
  });
};

/**
 * Performance monitoring hook
 * Tracks query performance for optimization
 */
export const useDashboardPerformance = () => {
  const queryClient = useQueryClient();
  
  const getPerformanceMetrics = () => {
    const cache = queryClient.getQueryCache();
    const dashboardQueries = cache.findAll({
      queryKey: ['optimized-dashboard']
    });
    
    return {
      totalQueries: dashboardQueries.length,
      cacheHitRate: dashboardQueries.filter(q => q.state.data).length / dashboardQueries.length,
      averageQueryTime: dashboardQueries.reduce((sum, q) => {
        const duration = q.state.dataUpdatedAt ? Date.now() - q.state.dataUpdatedAt : 0;
        return sum + duration;
      }, 0) / dashboardQueries.length,
      staleCacheCount: dashboardQueries.filter(q => q.isStale()).length
    };
  };
  
  return { getPerformanceMetrics };
};
