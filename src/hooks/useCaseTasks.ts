import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useCompany } from '@/contexts/CompanyContext';
import { useToast } from './use-toast';

export interface CaseTask {
  id: string;
  case_id: string;
  title: string;
  description?: string;
  status: string;
  deadline?: string;
  priority: string;
  assigned_to?: string;
  completed_at?: string;
  user_id: string;
  company_id: string;
  created_at: string;
  updated_at: string;
}

export const useCaseTasks = (caseId?: string) => {
  const [tasks, setTasks] = useState<CaseTask[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user, userRole } = useAuth();
  const { currentCompany } = useCompany();
  const { toast } = useToast();

  const fetchTasks = async () => {
    if (!user || !caseId) return;

    try {
      console.log('Fetching tasks for case:', caseId, 'user:', user.id);
      const { data, error } = await supabase
        .from('case_tasks')
        .select('*')
        .eq('case_id', caseId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching tasks:', error);
        throw error;
      }

      console.log('Fetched tasks:', data);
      setTasks(data || []);
    } catch (error) {
      console.error('Error fetching tasks:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה בטעינת המשימות",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const addTask = async (taskData: {
    case_id: string;
    title: string;
    description?: string;
    status?: string;
    deadline?: string;
    priority?: string;
    assigned_to?: string;
  }) => {
    if (!user || !currentCompany?.id) {
      console.error('Cannot create task: missing user or company', { user: !!user, company: !!currentCompany?.id });
      return;
    }

    try {
      const taskPayload = {
        ...taskData,
        user_id: user.id,
        company_id: currentCompany.id,
        status: taskData.status || 'לא הושלם',
        priority: taskData.priority || 'בינוני',
      };

      console.log('Creating task with payload:', taskPayload);

      const { data, error } = await supabase
        .from('case_tasks')
        .insert(taskPayload)
        .select()
        .single();

      if (error) {
        console.error('Database error creating task:', error);
        throw error;
      }

      console.log('Task created successfully:', data);
      setTasks(prev => [data, ...prev]);
      toast({
        title: "הצלחה",
        description: "המשימה נוצרה בהצלחה",
      });

      return data;
    } catch (error) {
      console.error('Error creating task:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה ביצירת המשימה",
        variant: "destructive",
      });
    }
  };

  const updateTask = async (taskId: string, updates: Partial<CaseTask>) => {
    try {
      const { data, error } = await supabase
        .from('case_tasks')
        .update(updates)
        .eq('id', taskId)
        .select()
        .single();

      if (error) throw error;

      setTasks(prev => prev.map(t => t.id === taskId ? data : t));
      toast({
        title: "הצלחה",
        description: "המשימה עודכנה בהצלחה",
      });

      return data;
    } catch (error) {
      console.error('Error updating task:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה בעדכון המשימה",
        variant: "destructive",
      });
    }
  };

  const deleteTask = async (taskId: string) => {
    try {
      const { error } = await supabase
        .from('case_tasks')
        .delete()
        .eq('id', taskId);

      if (error) throw error;

      setTasks(prev => prev.filter(t => t.id !== taskId));
      toast({
        title: "הצלחה",
        description: "המשימה נמחקה בהצלחה",
      });
    } catch (error) {
      console.error('Error deleting task:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה במחיקת המשימה",
        variant: "destructive",
      });
    }
  };

  const toggleTaskComplete = async (taskId: string) => {
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;

    const isCompleted = task.status === 'הושלם';
    const updates = {
      status: isCompleted ? 'לא הושלם' : 'הושלם',
      completed_at: isCompleted ? null : new Date().toISOString(),
    };

    await updateTask(taskId, updates);
  };

  useEffect(() => {
    if (user && caseId) {
      fetchTasks();
    }
  }, [user, caseId]);

  return {
    tasks,
    isLoading,
    addTask,
    updateTask,
    deleteTask,
    toggleTaskComplete,
    refetchTasks: fetchTasks,
  };
};