import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useCompany } from '@/contexts/CompanyContext';
import { toast } from 'sonner';
import { CreateCompanyData, UpdateCompanyData } from '@/types/company';

export const useCompanies = () => {
  const { allCompanies, handleCompanyCreated, handleCompanyDeleted, handleCompanyUpdated, refreshCompanies } = useCompany();
  const [isLoading, setIsLoading] = useState(true);



  const setupWebhook = async (companyId: string) => {
    try {
      const { data, error } = await supabase.functions.invoke('green-api-setup-webhook', {
        body: { companyId }
      });

      if (error) {
        console.error('Error setting up webhook:', error);
        toast.error('Failed to setup WhatsApp webhook');
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in setupWebhook:', error);
      toast.error('Failed to setup WhatsApp webhook');
      return null;
    }
  };

  const createCompany = async (companyData: CreateCompanyData) => {
    try {
      const { data, error } = await supabase
        .from('companies')
        .insert([companyData])
        .select()
        .single();

      if (error) {
        console.error('Error creating company:', error);
        toast.error('Failed to create company');
        throw error;
      }

      toast.success('Company created successfully');

      // Setup webhook if GreenAPI credentials are provided
      if (data && companyData.green_api_instance_id && companyData.green_api_token) {
        const webhookResult = await setupWebhook(data.id);
        if (webhookResult?.success) {
          toast.success('WhatsApp webhook configured successfully');
        }
      }

      // Use CompanyContext to handle the update
      await handleCompanyCreated(data);
      return data;
    } catch (error) {
      console.error('Error in createCompany:', error);
      throw error;
    }
  };

  const updateCompany = async (companyId: string, updates: UpdateCompanyData) => {
    try {
      const { error } = await supabase
        .from('companies')
        .update(updates)
        .eq('id', companyId);

      if (error) {
        console.error('Error updating company:', error);
        toast.error('Failed to update company');
        throw error;
      }

      toast.success('Company updated successfully');

      // Setup webhook if GreenAPI credentials are updated and both are present
      if (updates.green_api_instance_id && updates.green_api_token) {
        const webhookResult = await setupWebhook(companyId);
        if (webhookResult?.success) {
          toast.success('WhatsApp webhook configured successfully');
        }
      }

      // Use CompanyContext to handle the update
      await refreshCompanies();
    } catch (error) {
      console.error('Error in updateCompany:', error);
      throw error;
    }
  };

  const deleteCompany = async (companyId: string) => {
    try {
      const { error } = await supabase
        .from('companies')
        .delete()
        .eq('id', companyId);

      if (error) {
        console.error('Error deleting company:', error);
        toast.error('Failed to delete company');
        throw error;
      }

      toast.success('Company deleted successfully');
      await handleCompanyDeleted(companyId);
    } catch (error) {
      console.error('Error in deleteCompany:', error);
      throw error;
    }
  };

  useEffect(() => {
    setIsLoading(false); // Companies are managed by CompanyContext
  }, []);

  return {
    companies: allCompanies,
    isLoading,
    createCompany,
    updateCompany,
    deleteCompany,
    setupWebhook,
    refetchCompanies: refreshCompanies
  };
};