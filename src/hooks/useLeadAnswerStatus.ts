import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useCompany } from '@/contexts/CompanyContext';
import { toast } from 'sonner';

export interface LeadAnswerStatus {
  id: string;
  lead_id: string;
  company_id: string;
  main_status: string;
  answer_status: string;
  attempt_number: number;
  created_at: string;
  updated_at: string;
  created_by: string;
}

export const useLeadAnswerStatus = () => {
  const { currentCompany } = useCompany();

  const updateLeadAnswerStatus = async (leadId: string, newAttemptNumber: number) => {
    if (!currentCompany?.id) {
      throw new Error('No company selected');
    }

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Validate attempt number
      if (newAttemptNumber < 1 || newAttemptNumber > 7) {
        throw new Error('Attempt number must be between 1 and 7');
      }

      const answerStatus = `לא ענה ${newAttemptNumber}`;

      // Use the database function for more reliable upsert
      const { data, error } = await supabase
        .rpc('upsert_lead_answer_status', {
          p_lead_id: leadId,
          p_user_id: user.id,
          p_attempt_number: newAttemptNumber,
          p_answer_status: answerStatus
        });

      if (error) {
        console.error('Database error updating answer status:', error);
        throw error;
      }

      console.log('Answer status updated successfully:', data);

      // Trigger workflow for answer status change
      await triggerAnswerStatusWorkflow(leadId, answerStatus);

      toast.success(`סטטוס מענה עודכן ל: ${answerStatus}`);
      return data;

    } catch (error) {
      console.error('Error updating lead answer status:', error);
      toast.error('שגיאה בעדכון סטטוס המענה');
      throw error;
    }
  };

  const triggerAnswerStatusWorkflow = async (leadId: string, newAnswerStatus: string) => {
    try {
      const { error } = await supabase.functions.invoke('workflow-executor', {
        body: {
          action: 'trigger',
          triggerData: {
            entity_type: 'lead',
            entity_id: leadId,
            trigger_type: 'lead_answer_status_change',
            old_status: null, // We don't track old answer status for now
            new_status: newAnswerStatus,
            company_id: currentCompany?.id
          }
        }
      });

      if (error) {
        console.error('Error triggering answer status workflow:', error);
      }
    } catch (error) {
      console.error('Error calling workflow executor:', error);
    }
  };

  const getLeadAnswerStatus = async (leadId: string): Promise<LeadAnswerStatus | null> => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Use the database function to bypass RLS issues
      const { data, error } = await supabase
        .rpc('get_lead_answer_status', {
          p_lead_id: leadId,
          p_user_id: user.id
        });

      if (error) {
        // If the function throws an exception (like no record found), it's not an error
        if (error.message?.includes('Lead not found') || error.message?.includes('no rows')) {
          return null;
        }
        throw error;
      }

      return data || null;
    } catch (error) {
      console.error('Error fetching lead answer status:', error);
      return null;
    }
  };

  const getNextAttemptNumber = async (leadId: string): Promise<number> => {
    const currentStatus = await getLeadAnswerStatus(leadId);
    return currentStatus ? Math.min(currentStatus.attempt_number + 1, 7) : 1;
  };

  const resetAnswerStatus = async (leadId: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Use the database function to reset answer status (bypasses RLS issues)
      const { data, error } = await supabase
        .rpc('reset_lead_answer_status', {
          lead_uuid: leadId,
          user_uuid: user.id
        });

      if (error) {
        console.error('Database error resetting answer status:', error);
        throw error;
      }

      console.log('Answer status reset successfully for lead:', leadId);
      toast.success('סטטוס מענה אופס');
    } catch (error) {
      console.error('Error resetting answer status:', error);
      toast.error('שגיאה באיפוס סטטוס המענה');
      throw error;
    }
  };

  return {
    updateLeadAnswerStatus,
    getLeadAnswerStatus,
    getNextAttemptNumber,
    resetAnswerStatus,
    triggerAnswerStatusWorkflow
  };
};
