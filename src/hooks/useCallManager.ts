import { useState } from 'react';
import { useTwilioVoice, VoiceCall } from './useTwilioVoice';
import { useCompany } from '@/contexts/CompanyContext';
import { toast } from 'sonner';

export interface ActiveCall {
  leadId?: string;
  leadName?: string;
  phoneNumber?: string;
  startTime: Date;
  status: 'connecting' | 'connected' | 'disconnected' | 'failed';
  direction: 'outbound' | 'inbound';
}

export const useCallManager = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { currentCompany } = useCompany();

  const {
    device,
    isInitialized,
    isInitializing,
    initializationError,
    activeCall: voiceCall,
    makeCall: voiceMakeCall,
    hangupCall: voiceHangup,
    acceptCall,
    rejectCall,
    toggleMute,
    isMuted,
    initializeDevice,
    audioDevices,
    volume,
    setSpeakerDevice,
    setMicrophoneDevice,
    setCallVolume,
    updateAudioDevices
  } = useTwilioVoice({
    autoInitialize: false, // Changed to false for lazy initialization
    companyId: currentCompany?.id
  });

  // Convert VoiceCall to ActiveCall
  const activeCall: ActiveCall | null = voiceCall ? {
    leadId: voiceCall.leadId,
    leadName: voiceCall.leadName,
    phoneNumber: voiceCall.phoneNumber,
    startTime: voiceCall.startTime,
    status: voiceCall.status,
    direction: voiceCall.direction
  } : null;

  const initiateCall = async (leadId: string, leadName: string, phoneNumber: string) => {
    setIsLoading(true);
    try {
      // Initialize Twilio device if not already initialized
      if (!isInitialized && !isInitializing) {
        console.log('Initializing Twilio device for call...');
        await initializeDevice();
      }

      // Wait for initialization to complete if it's in progress
      if (isInitializing) {
        console.log('Waiting for Twilio initialization to complete...');
        // Wait up to 10 seconds for initialization
        let attempts = 0;
        while (isInitializing && attempts < 50) {
          await new Promise(resolve => setTimeout(resolve, 200));
          attempts++;
        }
      }

      if (!isInitialized) {
        throw new Error('Failed to initialize voice system');
      }

      await voiceMakeCall(phoneNumber, leadId, leadName);
    } catch (error) {
      console.error('Failed to initiate call:', error);
      toast.error('Failed to initiate call');
    } finally {
      setIsLoading(false);
    }
  };

  const makeCall = async (phoneNumber: string, leadId: string, leadName: string) => {
    return initiateCall(leadId, leadName, phoneNumber);
  };

  const hangupCall = () => {
    voiceHangup();
  };

  const muteCall = () => {
    if (!isMuted) {
      toggleMute();
    }
  };

  const unmuteCall = () => {
    if (isMuted) {
      toggleMute();
    }
  };

  return {
    // State
    device,
    isInitialized,
    isInitializing,
    initializationError,
    activeCall,
    isLoading,
    audioDevices,
    isMuted,
    volume,
    voiceCall, // For compatibility

    // Actions
    initializeDevice,
    makeCall,
    initiateCall,
    hangupCall,
    muteCall,
    unmuteCall,
    acceptCall,
    rejectCall,
    toggleMute,
    setSpeakerDevice,
    setMicrophoneDevice,
    setCallVolume,
    updateAudioDevices
  };
};
