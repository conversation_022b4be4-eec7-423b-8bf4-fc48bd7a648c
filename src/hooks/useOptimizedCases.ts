import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useCompany } from '@/contexts/CompanyContext';
import { useAuth } from './useAuth';
import { toast } from 'sonner';
import { Case, CaseType } from './useCases';

const CASES_PAGE_SIZE = 20;

/**
 * Optimized Cases Hook with React Query
 * Implements pagination, caching, and optimistic updates
 */
export const useOptimizedCases = () => {
  const { currentCompany } = useCompany();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Paginated cases query
  const casesQuery = useInfiniteQuery({
    queryKey: ['cases', currentCompany?.id],
    queryFn: async ({ pageParam = 0 }) => {
      if (!currentCompany?.id) throw new Error('No company selected');

      console.log(`📄 Fetching cases page ${pageParam}...`);
      const startTime = performance.now();

      const { data, error } = await supabase
        .from('cases')
        .select(`
          *,
          case_type:case_types(id, name, hourly_rate),
          lead:leads(id, full_name, phone, email)
        `)
        .eq('company_id', currentCompany.id)
        .order('created_at', { ascending: false })
        .range(pageParam * CASES_PAGE_SIZE, (pageParam + 1) * CASES_PAGE_SIZE - 1);

      const endTime = performance.now();
      console.log(`✅ Cases page ${pageParam} fetched in ${(endTime - startTime).toFixed(2)}ms`);

      if (error) throw error;
      return data || [];
    },
    enabled: !!currentCompany?.id,
    getNextPageParam: (lastPage, pages) => 
      lastPage.length === CASES_PAGE_SIZE ? pages.length : undefined,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });

  // Case types query
  const caseTypesQuery = useQuery({
    queryKey: ['case-types', currentCompany?.id],
    queryFn: async () => {
      if (!currentCompany?.id) throw new Error('No company selected');

      const { data, error } = await supabase
        .from('case_types')
        .select('*')
        .eq('company_id', currentCompany.id)
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      return data || [];
    },
    enabled: !!currentCompany?.id,
    staleTime: 10 * 60 * 1000, // 10 minutes - case types change rarely
    gcTime: 30 * 60 * 1000, // 30 minutes
  });

  // Add case mutation
  const addCaseMutation = useMutation({
    mutationFn: async (caseData: {
      title: string;
      description?: string;
      case_type_id?: string;
      status: string;
      value?: number;
      lead_id?: string;
      deadline?: string;
    }) => {
      if (!user || !currentCompany?.id) throw new Error('User or company not available');

      const { data, error } = await supabase
        .from('cases')
        .insert({
          ...caseData,
          user_id: user.id,
          company_id: currentCompany.id,
        })
        .select(`
          *,
          case_type:case_types(id, name, hourly_rate),
          lead:leads(id, full_name, phone, email)
        `)
        .single();

      if (error) throw error;
      return data;
    },
    onMutate: async (newCase) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['cases', currentCompany?.id] });

      // Snapshot previous value
      const previousCases = queryClient.getQueryData(['cases', currentCompany?.id]);

      // Optimistically update cache
      queryClient.setQueryData(['cases', currentCompany?.id], (old: any) => {
        if (!old) return old;
        
        const optimisticCase = {
          id: `temp-${Date.now()}`,
          ...newCase,
          user_id: user?.id,
          company_id: currentCompany?.id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          total_time_logged: 0,
        };

        return {
          ...old,
          pages: [
            [optimisticCase, ...old.pages[0]],
            ...old.pages.slice(1)
          ]
        };
      });

      return { previousCases };
    },
    onError: (err, newCase, context) => {
      // Rollback on error
      if (context?.previousCases) {
        queryClient.setQueryData(['cases', currentCompany?.id], context.previousCases);
      }
      toast.error('שגיאה ביצירת התיק');
    },
    onSuccess: (data) => {
      toast.success('התיק נוצר בהצלחה');
    },
    onSettled: () => {
      // Always refetch after mutation
      queryClient.invalidateQueries({ queryKey: ['cases', currentCompany?.id] });
    },
  });

  // Update case mutation
  const updateCaseMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<Case> }) => {
      const { data, error } = await supabase
        .from('cases')
        .update(updates)
        .eq('id', id)
        .select(`
          *,
          case_type:case_types(id, name, hourly_rate),
          lead:leads(id, full_name, phone, email)
        `)
        .single();

      if (error) throw error;
      return data;
    },
    onMutate: async ({ id, updates }) => {
      await queryClient.cancelQueries({ queryKey: ['cases', currentCompany?.id] });

      const previousCases = queryClient.getQueryData(['cases', currentCompany?.id]);

      // Optimistically update
      queryClient.setQueryData(['cases', currentCompany?.id], (old: any) => {
        if (!old) return old;

        return {
          ...old,
          pages: old.pages.map((page: Case[]) =>
            page.map((case_: Case) =>
              case_.id === id ? { ...case_, ...updates } : case_
            )
          )
        };
      });

      return { previousCases };
    },
    onError: (err, variables, context) => {
      if (context?.previousCases) {
        queryClient.setQueryData(['cases', currentCompany?.id], context.previousCases);
      }
      toast.error('שגיאה בעדכון התיק');
    },
    onSuccess: () => {
      toast.success('התיק עודכן בהצלחה');
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['cases', currentCompany?.id] });
    },
  });

  // Delete case mutation
  const deleteCaseMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('cases')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return id;
    },
    onMutate: async (id) => {
      await queryClient.cancelQueries({ queryKey: ['cases', currentCompany?.id] });

      const previousCases = queryClient.getQueryData(['cases', currentCompany?.id]);

      // Optimistically remove
      queryClient.setQueryData(['cases', currentCompany?.id], (old: any) => {
        if (!old) return old;

        return {
          ...old,
          pages: old.pages.map((page: Case[]) =>
            page.filter((case_: Case) => case_.id !== id)
          )
        };
      });

      return { previousCases };
    },
    onError: (err, id, context) => {
      if (context?.previousCases) {
        queryClient.setQueryData(['cases', currentCompany?.id], context.previousCases);
      }
      toast.error('שגיאה במחיקת התיק');
    },
    onSuccess: () => {
      toast.success('התיק נמחק בהצלחה');
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['cases', currentCompany?.id] });
    },
  });

  // Flatten paginated data
  const cases = casesQuery.data?.pages.flat() || [];
  const caseTypes = caseTypesQuery.data || [];

  // Get case by ID (from cache)
  const getCaseById = (id: string): Case | undefined => {
    return cases.find(case_ => case_.id === id);
  };

  // Prefetch next page
  const prefetchNextPage = () => {
    if (casesQuery.hasNextPage && !casesQuery.isFetchingNextPage) {
      casesQuery.fetchNextPage();
    }
  };

  return {
    // Data
    cases,
    caseTypes,
    
    // Loading states
    isLoading: casesQuery.isLoading || caseTypesQuery.isLoading,
    isFetching: casesQuery.isFetching || caseTypesQuery.isFetching,
    isLoadingMore: casesQuery.isFetchingNextPage,
    
    // Pagination
    hasNextPage: casesQuery.hasNextPage,
    fetchNextPage: casesQuery.fetchNextPage,
    prefetchNextPage,
    
    // Mutations
    addCase: addCaseMutation.mutate,
    updateCase: updateCaseMutation.mutate,
    deleteCase: deleteCaseMutation.mutate,
    
    // Mutation states
    isAddingCase: addCaseMutation.isPending,
    isUpdatingCase: updateCaseMutation.isPending,
    isDeletingCase: deleteCaseMutation.isPending,
    
    // Utilities
    getCaseById,
    refetch: () => {
      casesQuery.refetch();
      caseTypesQuery.refetch();
    },
    
    // Error states
    error: casesQuery.error || caseTypesQuery.error,
    isError: casesQuery.isError || caseTypesQuery.isError,
  };
};
