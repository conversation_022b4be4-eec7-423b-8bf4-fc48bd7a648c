/**
 * Utility functions for password generation and validation
 */

/**
 * Generates a secure random password
 * @param length - Length of the password (default: 12)
 * @param includeSymbols - Whether to include special symbols (default: true)
 * @returns A secure random password
 */
export function generateSecurePassword(length: number = 12, includeSymbols: boolean = true): string {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
  
  let charset = lowercase + uppercase + numbers;
  if (includeSymbols) {
    charset += symbols;
  }
  
  let password = '';
  
  // Ensure at least one character from each required set
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  
  if (includeSymbols) {
    password += symbols[Math.floor(Math.random() * symbols.length)];
  }
  
  // Fill the rest randomly
  for (let i = password.length; i < length; i++) {
    password += charset[Math.floor(Math.random() * charset.length)];
  }
  
  // Shuffle the password to avoid predictable patterns
  return password.split('').sort(() => Math.random() - 0.5).join('');
}

/**
 * Validates password strength
 * @param password - Password to validate
 * @returns Object with validation results
 */
export function validatePasswordStrength(password: string) {
  const minLength = 8;
  const hasLowercase = /[a-z]/.test(password);
  const hasUppercase = /[A-Z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSymbols = /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password);
  const isLongEnough = password.length >= minLength;
  
  const score = [hasLowercase, hasUppercase, hasNumbers, hasSymbols, isLongEnough]
    .filter(Boolean).length;
  
  let strength: 'weak' | 'fair' | 'good' | 'strong';
  if (score < 3) strength = 'weak';
  else if (score < 4) strength = 'fair';
  else if (score < 5) strength = 'good';
  else strength = 'strong';
  
  return {
    isValid: score >= 4, // Require at least 4 criteria
    strength,
    score,
    criteria: {
      hasLowercase,
      hasUppercase,
      hasNumbers,
      hasSymbols,
      isLongEnough
    },
    suggestions: [
      !isLongEnough && `Password should be at least ${minLength} characters long`,
      !hasLowercase && 'Include lowercase letters',
      !hasUppercase && 'Include uppercase letters',
      !hasNumbers && 'Include numbers',
      !hasSymbols && 'Include special symbols'
    ].filter(Boolean)
  };
}

/**
 * Generates a memorable but secure password using word combinations
 * @returns A memorable password
 */
export function generateMemorablePassword(): string {
  const adjectives = ['Quick', 'Bright', 'Swift', 'Bold', 'Smart', 'Clear', 'Strong', 'Fast'];
  const nouns = ['Tiger', 'Eagle', 'River', 'Mountain', 'Ocean', 'Forest', 'Storm', 'Fire'];
  const numbers = Math.floor(Math.random() * 100).toString().padStart(2, '0');
  const symbols = ['!', '@', '#', '$', '%'];
  
  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
  const noun = nouns[Math.floor(Math.random() * nouns.length)];
  const symbol = symbols[Math.floor(Math.random() * symbols.length)];
  
  return `${adjective}${noun}${numbers}${symbol}`;
}

/**
 * Simple password generator for user creation
 * @returns A secure 10-character password
 */
export function generatePassword(): string {
  return generateSecurePassword(10, false); // 10 chars, no symbols for simplicity
}
