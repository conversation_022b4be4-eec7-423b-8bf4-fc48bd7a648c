// API Routes for external integrations (AI Agent, Webhooks, etc.)
// These can be used to create actual API endpoints in your backend

import { ApiClient } from './api-client';

export interface ApiRequest {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers: Record<string, string>;
  body?: any;
  params?: Record<string, string>;
  query?: Record<string, string>;
}

export interface ApiResponse {
  status: number;
  data?: any;
  error?: string;
  message?: string;
}

export class ApiRoutes {
  private apiClient: ApiClient;

  constructor() {
    this.apiClient = ApiClient.getInstance();
  }

  // Authentication middleware
  private async authenticate(request: ApiRequest): Promise<{ user_id: string; company_id: string } | null> {
    const authHeader = request.headers['authorization'];
    const apiKey = request.headers['x-api-key'];

    // For AI agent integration, use API key authentication
    if (apiKey) {
      // In production, validate API key against your database
      // For now, return mock user data
      return {
        user_id: 'ai-agent-user-id',
        company_id: 'default-company-id'
      };
    }

    // For regular users, validate JWT token
    if (authHeader?.startsWith('Bearer ')) {
      // Validate JWT token here
      // Return user info from token
    }

    return null;
  }

  // Lead Management Routes
  async handleLeadRoutes(path: string, request: ApiRequest): Promise<ApiResponse> {
    const auth = await this.authenticate(request);
    if (!auth) {
      return { status: 401, error: 'Unauthorized' };
    }

    try {
      switch (path) {
        case '/api/leads':
          if (request.method === 'POST') {
            const result = await this.apiClient.createLead({
              ...request.body,
              company_id: auth.company_id,
              user_id: auth.user_id
            });
            return result.success 
              ? { status: 201, data: result.data }
              : { status: 400, error: result.error };
          }
          break;

        case '/api/leads/search':
          if (request.method === 'GET') {
            const query = request.query?.q || '';
            const result = await this.apiClient.searchLeads(query, auth.company_id);
            return result.success 
              ? { status: 200, data: result.data }
              : { status: 400, error: result.error };
          }
          break;

        default:
          // Handle dynamic routes like /api/leads/:id/status
          const leadIdMatch = path.match(/^\/api\/leads\/([^\/]+)\/status$/);
          if (leadIdMatch && request.method === 'PUT') {
            const leadId = leadIdMatch[1];
            const { status, notes } = request.body;
            const result = await this.apiClient.updateLeadStatus(leadId, status, notes);
            return result.success 
              ? { status: 200, data: result.data }
              : { status: 400, error: result.error };
          }
      }
    } catch (error) {
      return { status: 500, error: 'Internal server error' };
    }

    return { status: 404, error: 'Route not found' };
  }

  // Case Management Routes
  async handleCaseRoutes(path: string, request: ApiRequest): Promise<ApiResponse> {
    const auth = await this.authenticate(request);
    if (!auth) {
      return { status: 401, error: 'Unauthorized' };
    }

    try {
      switch (path) {
        case '/api/cases':
          if (request.method === 'POST') {
            const result = await this.apiClient.createCase({
              ...request.body,
              company_id: auth.company_id,
              user_id: auth.user_id
            });
            return result.success 
              ? { status: 201, data: result.data }
              : { status: 400, error: result.error };
          }
          break;

        case '/api/cases/search':
          if (request.method === 'GET') {
            const query = request.query?.q || '';
            const result = await this.apiClient.searchCases(query, auth.company_id);
            return result.success 
              ? { status: 200, data: result.data }
              : { status: 400, error: result.error };
          }
          break;

        case '/api/case-types':
          if (request.method === 'GET') {
            const result = await this.apiClient.getCaseTypes(auth.company_id);
            return result.success 
              ? { status: 200, data: result.data }
              : { status: 400, error: result.error };
          }
          break;

        default:
          // Handle dynamic routes like /api/cases/:id/status
          const caseIdMatch = path.match(/^\/api\/cases\/([^\/]+)\/status$/);
          if (caseIdMatch && request.method === 'PUT') {
            const caseId = caseIdMatch[1];
            const { status, notes } = request.body;
            const result = await this.apiClient.updateCaseStatus(caseId, status, notes);
            return result.success 
              ? { status: 200, data: result.data }
              : { status: 400, error: result.error };
          }

          // Handle time entries
          const timeEntryMatch = path.match(/^\/api\/cases\/([^\/]+)\/time-entries$/);
          if (timeEntryMatch && request.method === 'POST') {
            const caseId = timeEntryMatch[1];
            const result = await this.apiClient.addTimeEntry({
              ...request.body,
              case_id: caseId,
              company_id: auth.company_id,
              user_id: auth.user_id
            });
            return result.success 
              ? { status: 201, data: result.data }
              : { status: 400, error: result.error };
          }
      }
    } catch (error) {
      return { status: 500, error: 'Internal server error' };
    }

    return { status: 404, error: 'Route not found' };
  }

  // WhatsApp Integration Routes
  async handleWhatsAppRoutes(path: string, request: ApiRequest): Promise<ApiResponse> {
    const auth = await this.authenticate(request);
    if (!auth) {
      return { status: 401, error: 'Unauthorized' };
    }

    try {
      switch (path) {
        case '/api/whatsapp/activity':
          if (request.method === 'POST') {
            const result = await this.apiClient.logWhatsAppActivity({
              ...request.body,
              company_id: auth.company_id,
              user_id: auth.user_id
            });
            return result.success 
              ? { status: 201, data: result.data }
              : { status: 400, error: result.error };
          }
          break;
      }
    } catch (error) {
      return { status: 500, error: 'Internal server error' };
    }

    return { status: 404, error: 'Route not found' };
  }

  // Main router
  async handleRequest(path: string, request: ApiRequest): Promise<ApiResponse> {
    if (path.startsWith('/api/leads')) {
      return this.handleLeadRoutes(path, request);
    } else if (path.startsWith('/api/cases') || path.startsWith('/api/case-types')) {
      return this.handleCaseRoutes(path, request);
    } else if (path.startsWith('/api/whatsapp')) {
      return this.handleWhatsAppRoutes(path, request);
    }

    return { status: 404, error: 'API route not found' };
  }
}

// Example usage for AI Agent integration
export const createAIAgentHelpers = () => {
  const apiRoutes = new ApiRoutes();

  return {
    // Create a new lead from WhatsApp conversation
    async createLeadFromWhatsApp(phoneNumber: string, name: string, message: string) {
      return await apiRoutes.handleRequest('/api/leads', {
        method: 'POST',
        headers: { 'x-api-key': 'ai-agent-key' },
        body: {
          full_name: name,
          phone: phoneNumber,
          source: 'WhatsApp AI Agent',
          status: 'ליד חדש',
          notes: `הודעה ראשונה: ${message}`
        }
      });
    },

    // Create a case from AI conversation
    async createCaseFromConversation(title: string, description: string, leadId?: string) {
      return await apiRoutes.handleRequest('/api/cases', {
        method: 'POST',
        headers: { 'x-api-key': 'ai-agent-key' },
        body: {
          title,
          description,
          lead_id: leadId,
          status: 'בקליטה'
        }
      });
    },

    // Update case status
    async updateCaseStatus(caseId: string, status: string, notes?: string) {
      return await apiRoutes.handleRequest(`/api/cases/${caseId}/status`, {
        method: 'PUT',
        headers: { 'x-api-key': 'ai-agent-key' },
        body: { status, notes }
      });
    },

    // Add time entry
    async addTimeEntry(caseId: string, description: string, duration: number, hourlyRate?: number) {
      return await apiRoutes.handleRequest(`/api/cases/${caseId}/time-entries`, {
        method: 'POST',
        headers: { 'x-api-key': 'ai-agent-key' },
        body: {
          description,
          duration,
          hourly_rate: hourlyRate
        }
      });
    },

    // Log WhatsApp activity
    async logWhatsAppMessage(phoneNumber: string, messageType: 'incoming' | 'outgoing', content: string, leadId?: string) {
      return await apiRoutes.handleRequest('/api/whatsapp/activity', {
        method: 'POST',
        headers: { 'x-api-key': 'ai-agent-key' },
        body: {
          phone_number: phoneNumber,
          message_type: messageType,
          message_content: content,
          lead_id: leadId
        }
      });
    },

    // Search for existing leads
    async searchLeads(query: string) {
      return await apiRoutes.handleRequest('/api/leads/search', {
        method: 'GET',
        headers: { 'x-api-key': 'ai-agent-key' },
        query: { q: query }
      });
    },

    // Search for existing cases
    async searchCases(query: string) {
      return await apiRoutes.handleRequest('/api/cases/search', {
        method: 'GET',
        headers: { 'x-api-key': 'ai-agent-key' },
        query: { q: query }
      });
    }
  };
};
