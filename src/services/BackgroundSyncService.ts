/**
 * Background Sync Service for 100+ Companies Scalability
 * Handles background data synchronization, caching, and performance optimization
 */

import { supabase } from '@/integrations/supabase/client';

interface SyncConfig {
  interval: number; // milliseconds
  batchSize: number;
  maxRetries: number;
  priority: 'high' | 'medium' | 'low';
}

interface SyncJob {
  id: string;
  type: 'dashboard' | 'leads' | 'cases' | 'time_entries';
  companyId: string;
  config: SyncConfig;
  lastSync: Date | null;
  nextSync: Date;
  status: 'pending' | 'running' | 'completed' | 'failed';
  retryCount: number;
  data?: any;
  error?: string;
}

class BackgroundSyncService {
  private static instance: BackgroundSyncService;
  private syncJobs: Map<string, SyncJob> = new Map();
  private isRunning = false;
  private syncInterval: NodeJS.Timeout | null = null;
  private workers: Map<string, Worker> = new Map();

  static getInstance(): BackgroundSyncService {
    if (!BackgroundSyncService.instance) {
      BackgroundSyncService.instance = new BackgroundSyncService();
    }
    return BackgroundSyncService.instance;
  }

  private constructor() {
    // Initialize service worker if available
    this.initializeServiceWorker();
  }

  private async initializeServiceWorker() {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');
        console.log('📡 Background sync service worker registered:', registration);
      } catch (error) {
        console.warn('⚠️ Service worker registration failed:', error);
      }
    }
  }

  /**
   * Start the background sync service
   */
  start() {
    if (this.isRunning) return;

    this.isRunning = true;
    console.log('🚀 Starting background sync service...');

    // Run sync every 30 seconds
    this.syncInterval = setInterval(() => {
      this.processSyncJobs();
    }, 30000);

    // Initial sync
    this.processSyncJobs();
  }

  /**
   * Stop the background sync service
   */
  stop() {
    if (!this.isRunning) return;

    this.isRunning = false;
    console.log('⏹️ Stopping background sync service...');

    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    // Terminate all workers
    this.workers.forEach(worker => worker.terminate());
    this.workers.clear();
  }

  /**
   * Schedule a sync job for a company
   */
  scheduleSync(
    type: SyncJob['type'],
    companyId: string,
    config: Partial<SyncConfig> = {}
  ): string {
    const jobId = `${type}-${companyId}-${Date.now()}`;
    
    const defaultConfig: SyncConfig = {
      interval: 5 * 60 * 1000, // 5 minutes
      batchSize: 100,
      maxRetries: 3,
      priority: 'medium',
      ...config
    };

    const job: SyncJob = {
      id: jobId,
      type,
      companyId,
      config: defaultConfig,
      lastSync: null,
      nextSync: new Date(),
      status: 'pending',
      retryCount: 0
    };

    this.syncJobs.set(jobId, job);
    console.log(`📅 Scheduled ${type} sync for company ${companyId}`);

    return jobId;
  }

  /**
   * Process pending sync jobs
   */
  private async processSyncJobs() {
    const now = new Date();
    const pendingJobs = Array.from(this.syncJobs.values())
      .filter(job => job.status === 'pending' && job.nextSync <= now)
      .sort((a, b) => {
        // Sort by priority and next sync time
        const priorityOrder = { high: 0, medium: 1, low: 2 };
        const priorityDiff = priorityOrder[a.config.priority] - priorityOrder[b.config.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return a.nextSync.getTime() - b.nextSync.getTime();
      });

    // Process up to 3 jobs concurrently
    const concurrentJobs = pendingJobs.slice(0, 3);
    
    await Promise.all(
      concurrentJobs.map(job => this.executeSync(job))
    );
  }

  /**
   * Execute a sync job
   */
  private async executeSync(job: SyncJob) {
    try {
      job.status = 'running';
      console.log(`🔄 Executing ${job.type} sync for company ${job.companyId}`);

      const startTime = performance.now();
      let data: any;

      switch (job.type) {
        case 'dashboard':
          data = await this.syncDashboardData(job.companyId);
          break;
        case 'leads':
          data = await this.syncLeadsData(job.companyId, job.config.batchSize);
          break;
        case 'cases':
          data = await this.syncCasesData(job.companyId, job.config.batchSize);
          break;
        case 'time_entries':
          data = await this.syncTimeEntriesData(job.companyId, job.config.batchSize);
          break;
        default:
          throw new Error(`Unknown sync type: ${job.type}`);
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      job.status = 'completed';
      job.lastSync = new Date();
      job.nextSync = new Date(Date.now() + job.config.interval);
      job.data = data;
      job.retryCount = 0;

      console.log(`✅ ${job.type} sync completed for company ${job.companyId} in ${duration.toFixed(2)}ms`);

      // Cache the data
      await this.cacheData(job.type, job.companyId, data);

    } catch (error) {
      console.error(`❌ ${job.type} sync failed for company ${job.companyId}:`, error);
      
      job.error = error instanceof Error ? error.message : 'Unknown error';
      job.retryCount++;

      if (job.retryCount >= job.config.maxRetries) {
        job.status = 'failed';
        job.nextSync = new Date(Date.now() + 60 * 60 * 1000); // Retry in 1 hour
      } else {
        job.status = 'pending';
        // Exponential backoff
        const backoffDelay = Math.min(1000 * Math.pow(2, job.retryCount), 30000);
        job.nextSync = new Date(Date.now() + backoffDelay);
      }
    }
  }

  /**
   * Sync dashboard data for a company
   */
  private async syncDashboardData(companyId: string) {
    const dateFrom = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    const dateTo = new Date();

    const { data, error } = await supabase.rpc('get_dashboard_metrics', {
      p_company_id: companyId,
      p_date_from: dateFrom.toISOString(),
      p_date_to: dateTo.toISOString()
    });

    if (error) throw error;
    return data;
  }

  /**
   * Sync leads data for a company
   */
  private async syncLeadsData(companyId: string, batchSize: number) {
    const { data, error } = await supabase
      .from('leads')
      .select('*')
      .eq('company_id', companyId)
      .order('created_at', { ascending: false })
      .limit(batchSize);

    if (error) throw error;
    return data;
  }

  /**
   * Sync cases data for a company
   */
  private async syncCasesData(companyId: string, batchSize: number) {
    const { data, error } = await supabase
      .from('cases')
      .select(`
        *,
        case_type:case_types(id, name, hourly_rate),
        lead:leads(id, full_name, phone, email)
      `)
      .eq('company_id', companyId)
      .order('created_at', { ascending: false })
      .limit(batchSize);

    if (error) throw error;
    return data;
  }

  /**
   * Sync time entries data for a company
   */
  private async syncTimeEntriesData(companyId: string, batchSize: number) {
    const { data, error } = await supabase
      .from('case_time_entries')
      .select('*')
      .eq('company_id', companyId)
      .order('created_at', { ascending: false })
      .limit(batchSize);

    if (error) throw error;
    return data;
  }

  /**
   * Cache data in localStorage with compression
   */
  private async cacheData(type: string, companyId: string, data: any) {
    try {
      const cacheKey = `sync_${type}_${companyId}`;
      const cacheData = {
        data,
        timestamp: Date.now(),
        companyId,
        type
      };

      // Compress data if it's large
      const serialized = JSON.stringify(cacheData);
      if (serialized.length > 1024 * 1024) { // 1MB
        console.warn(`⚠️ Large cache data for ${type} (${serialized.length} bytes)`);
      }

      localStorage.setItem(cacheKey, serialized);
      
      // Set expiration
      const expirationKey = `${cacheKey}_expires`;
      const expirationTime = Date.now() + (10 * 60 * 1000); // 10 minutes
      localStorage.setItem(expirationKey, expirationTime.toString());

    } catch (error) {
      console.error('Failed to cache data:', error);
    }
  }

  /**
   * Get cached data
   */
  getCachedData(type: string, companyId: string): any | null {
    try {
      const cacheKey = `sync_${type}_${companyId}`;
      const expirationKey = `${cacheKey}_expires`;
      
      const expirationTime = localStorage.getItem(expirationKey);
      if (!expirationTime || Date.now() > parseInt(expirationTime)) {
        // Cache expired
        localStorage.removeItem(cacheKey);
        localStorage.removeItem(expirationKey);
        return null;
      }

      const cached = localStorage.getItem(cacheKey);
      if (!cached) return null;

      const cacheData = JSON.parse(cached);
      return cacheData.data;

    } catch (error) {
      console.error('Failed to get cached data:', error);
      return null;
    }
  }

  /**
   * Clear cache for a company
   */
  clearCache(companyId: string) {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.includes(`_${companyId}`)) {
        localStorage.removeItem(key);
      }
    });
  }

  /**
   * Get sync status for a company
   */
  getSyncStatus(companyId: string): { [key: string]: SyncJob } {
    const companyJobs: { [key: string]: SyncJob } = {};
    
    this.syncJobs.forEach(job => {
      if (job.companyId === companyId) {
        companyJobs[job.type] = job;
      }
    });

    return companyJobs;
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    const jobs = Array.from(this.syncJobs.values());
    const completed = jobs.filter(j => j.status === 'completed');
    const failed = jobs.filter(j => j.status === 'failed');
    const running = jobs.filter(j => j.status === 'running');

    return {
      totalJobs: jobs.length,
      completedJobs: completed.length,
      failedJobs: failed.length,
      runningJobs: running.length,
      successRate: jobs.length > 0 ? (completed.length / jobs.length) * 100 : 0,
      averageRetries: jobs.reduce((sum, j) => sum + j.retryCount, 0) / jobs.length || 0
    };
  }
}

export default BackgroundSyncService;
