import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useCaseTasks } from "@/hooks/useCaseTasks";

interface TaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  caseId?: string;
}

export const TaskModal = ({ isOpen, onClose, caseId }: TaskModalProps) => {
  const { toast } = useToast();
  const { addTask } = useCaseTasks(caseId);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    priority: "בינוני",
    deadline: "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      toast({
        title: "שגיאה",
        description: "נא להזין כותרת למשימה",
        variant: "destructive",
      });
      return;
    }

    if (!caseId) {
      toast({
        title: "שגיאה",
        description: "מזהה התיק לא נמצא",
        variant: "destructive",
      });
      return;
    }

    try {
      await addTask({
        case_id: caseId,
        title: formData.title.trim(),
        description: formData.description.trim() || undefined,
        priority: formData.priority,
        deadline: formData.deadline || undefined,
      });
      
      setFormData({
        title: "",
        description: "",
        priority: "בינוני",
        deadline: "",
      });
      
      onClose();
    } catch (error) {
      // Error handled in hook
    }
  };

  const priorityOptions = [
    "נמוך",
    "בינוני", 
    "גבוה",
    "קריטי"
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md hebrew-text" dir="rtl">
        <DialogHeader>
          <DialogTitle>הוספת משימה חדשה</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4 form-professional">
          <div className="space-y-2">
            <Label htmlFor="title">כותרת המשימה</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="כותרת המשימה..."
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">תיאור המשימה</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="תיאור המשימה..."
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="priority">עדיפות</Label>
              <Select 
                value={formData.priority} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {priorityOptions.map((priority) => (
                    <SelectItem key={priority} value={priority}>
                      {priority}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="deadline">תאריך יעד</Label>
              <Input
                id="deadline"
                type="datetime-local"
                value={formData.deadline}
                onChange={(e) => setFormData(prev => ({ ...prev, deadline: e.target.value }))}
              />
            </div>
          </div>


          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              ביטול
            </Button>
            <Button type="submit" className="btn-professional">
              הוסף משימה
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};