import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Plus, Play, Pause, Clock, Timer, Loader2 } from "lucide-react";
import { TaskModal } from "./TaskModal";
import { useCaseTasks } from "@/hooks/useCaseTasks";
import { useCaseTimeEntries } from "@/hooks/useCaseTimeEntries";

interface TasksSectionProps {
  caseId?: string;
}

export const TasksSection = ({ caseId }: TasksSectionProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { tasks, isLoading: tasksLoading, toggleTaskComplete } = useCaseTasks(caseId);
  const { startTimer, stopTimer, activeTimer, isLoading: timerLoading } = useCaseTimeEntries(caseId);

  const handleToggleTimer = async (taskId: string) => {
    if (activeTimer && activeTimer.id === taskId) {
      await stopTimer();
    } else {
      await startTimer(`עבודה על משימה`);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "הושלם":
        return "bg-success/10 text-success border-success/20";
      case "בתהליך":
        return "bg-primary/10 text-primary border-primary/20";
      case "ממתין":
        return "bg-warning/10 text-warning border-warning/20";
      default:
        return "bg-muted text-muted-foreground border-border";
    }
  };

  return (
    <Card className="card-professional">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Timer className="w-5 h-5" />
            משימות
          </CardTitle>
          <Button 
            className="btn-professional flex items-center gap-2"
            onClick={() => setIsModalOpen(true)}
          >
            <Plus className="w-4 h-4" />
            הוסף משימה חדשה
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {tasksLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin" />
          </div>
        ) : (
          <div className="space-y-4">
            {tasks.map((task) => (
              <div
                key={task.id}
                className="p-4 border border-border rounded-lg hover:bg-muted/30 transition-colors"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex-1">
                    <h4 className="font-medium text-foreground mb-1">
                      {task.title}
                    </h4>
                    <span className={`px-2 py-1 rounded-md text-xs border ${getStatusColor(task.status)}`}>
                      {task.status}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-sm text-foreground">
                    <Clock className="w-4 h-4" />
                    <span>זמן רשום: 0 דקות</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant={activeTimer?.id === task.id ? "destructive" : "default"}
                      onClick={() => handleToggleTimer(task.id)}
                      className="flex items-center gap-1"
                      disabled={timerLoading}
                    >
                      {activeTimer?.id === task.id ? (
                        <>
                          <Pause className="w-4 h-4" />
                          עצור טיימר
                        </>
                      ) : (
                        <>
                          <Play className="w-4 h-4" />
                          התחל טיימר
                        </>
                      )}
                    </Button>
                    
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => toggleTaskComplete(task.id)}
                    >
                      {task.status === "הושלם" ? "בטל השלמה" : "השלם"}
                    </Button>
                  </div>
                </div>
              </div>
          ))}
          
            {tasks.length === 0 && (
              <div className="text-center py-8 text-foreground">
                <Timer className="w-12 h-12 mx-auto mb-4" />
                <p>אין משימות עדיין</p>
                <p className="text-sm">הוסף משימה חדשה כדי להתחיל לעקוב אחר הזמן</p>
              </div>
            )}
          </div>
        )}
      </CardContent>

      <TaskModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        caseId={caseId}
      />
    </Card>
  );
};