import { Phone, PhoneOff, Mic, <PERSON>c<PERSON><PERSON>, <PERSON>, PhoneIncoming } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ActiveCall } from '@/hooks/useCallManager';
import { useEffect, useState } from 'react';

interface ActiveCallBarProps {
  activeCall: ActiveCall;
  onHangup: () => void;
  onMute: () => void;
  onUnmute: () => void;
  onAccept?: () => void;
  onReject?: () => void;
  isMuted?: boolean;
}

export const ActiveCallBar = ({
  activeCall,
  onHangup,
  onMute,
  onUnmute,
  onAccept,
  onReject,
  isMuted = false
}: ActiveCallBarProps) => {
  const [duration, setDuration] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      const elapsed = Math.floor((now.getTime() - activeCall.startTime.getTime()) / 1000);
      setDuration(elapsed);
    }, 1000);

    return () => clearInterval(interval);
  }, [activeCall.startTime]);

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusColor = () => {
    switch (activeCall.status) {
      case 'connecting':
        return 'text-warning animate-pulse';
      case 'connected':
        return 'text-success';
      case 'failed':
        return 'text-destructive';
      default:
        return 'text-muted-foreground';
    }
  };

  const getCallIcon = () => {
    if (activeCall.direction === 'inbound') {
      return <PhoneIncoming className={`w-5 h-5 ${getStatusColor()}`} />;
    }
    return <Phone className={`w-5 h-5 ${getStatusColor()}`} />;
  };

  const isIncomingCall = activeCall.direction === 'inbound' && activeCall.status === 'connecting';

  return (
    <Card className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-background border shadow-lg">
      <div className="flex items-center gap-4 p-4">
        <div className="flex items-center gap-2">
          {getCallIcon()}
          <div>
            <p className="font-semibold text-foreground">
              {activeCall.leadName || activeCall.phoneNumber || 'Unknown'}
            </p>
            {activeCall.phoneNumber && (
              <p className="text-sm text-muted-foreground">{activeCall.phoneNumber}</p>
            )}
            {activeCall.direction === 'inbound' && (
              <p className="text-xs text-blue-600">Incoming Call</p>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2 text-sm">
          <Clock className="w-4 h-4 text-muted-foreground" />
          <span className="font-mono text-foreground">{formatDuration(duration)}</span>
        </div>

        <div className="flex items-center gap-2">
          {isIncomingCall ? (
            // Incoming call controls
            <>
              <Button
                size="sm"
                variant="default"
                onClick={onAccept}
                className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
              >
                <Phone className="w-4 h-4" />
                ענה
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onClick={onReject}
                className="flex items-center gap-1"
              >
                <PhoneOff className="w-4 h-4" />
                דחה
              </Button>
            </>
          ) : (
            // Active call controls
            <>
              {isMuted ? (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={onUnmute}
                  className="flex items-center gap-1"
                >
                  <Mic className="w-4 h-4" />
                  בטל השתקה
                </Button>
              ) : (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={onMute}
                  className="flex items-center gap-1"
                >
                  <MicOff className="w-4 h-4" />
                  השתק
                </Button>
              )}

              <Button
                size="sm"
                variant="destructive"
                onClick={onHangup}
                className="flex items-center gap-1"
              >
                <PhoneOff className="w-4 h-4" />
                נתק
              </Button>
            </>
          )}
        </div>
      </div>
    </Card>
  );
};
