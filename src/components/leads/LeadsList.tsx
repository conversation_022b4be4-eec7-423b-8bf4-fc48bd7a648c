import React from 'react';
import { Lead, LeadCard } from './LeadCard';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';

interface LeadsListProps {
  leads: Lead[];
  onCall: (leadId: string, leadName: string, phoneNumber: string) => void;
  onEdit: (lead: Lead) => void;
  onDelete: (leadId: string) => void;
  onWhatsApp: (phoneNumber: string) => void;
  isCallLoading: boolean;
  onRefresh?: () => void;
}

interface StatusColumn {
  status: string;
  leads: Lead[];
  count: number;
  totalValue: number;
}

export const LeadsList = ({ leads, onCall, onEdit, onDelete, onWhatsApp, isCallLoading, onRefresh }: LeadsListProps) => {
  const statusOptions = [
    "ליד חדש",
    "צריך פולואפ",
    "לקוח סגור",
    "לא ענה",
    "לא מעוניין",
    "לא מתאים"
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ליד חדש":
        return "bg-primary text-primary-foreground";
      case "צריך פולואפ":
        return "bg-warning text-warning-foreground";
      case "לקוח סגור":
        return "bg-success text-success-foreground";
      case "לא ענה":
      case "לא מעוניין":
      case "לא מתאים":
        return "bg-muted text-muted-foreground";
      default:
        return "bg-muted text-muted-foreground";
    }
  };

  const formatValue = (value: number) => {
    if (!value) return '₪0';
    return `₪${value.toLocaleString()}`;
  };

  // Group leads by status and calculate totals
  const statusColumns: StatusColumn[] = statusOptions
    .map(status => {
      const statusLeads = leads.filter(lead => lead.status === status);
      const totalValue = statusLeads.reduce((sum, lead) => sum + (lead.value || 0), 0);

      return {
        status,
        leads: statusLeads,
        count: statusLeads.length,
        totalValue
      };
    })
    .filter(column => column.count > 0); // Only show columns with leads

  if (statusColumns.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        אין לידים להצגה
      </div>
    );
  }

  return (
    <div className="h-[calc(100vh-220px)] overflow-hidden">
      {/* Horizontal scrolling container for columns */}
      <div className="h-full overflow-x-auto overflow-y-hidden">
        <div className="flex gap-6 h-full min-w-max p-1">
          {statusColumns.map((column) => (
            <div key={column.status} className="flex-shrink-0 w-80">
              {/* Column Header */}
              <div className="mb-4 p-3 bg-card rounded-lg border">
                <div className="flex items-center justify-between mb-2">
                  <Badge className={`text-sm ${getStatusColor(column.status)}`}>
                    {column.status}
                  </Badge>
                  <span className="text-sm font-medium text-muted-foreground">
                    {column.count} לידים
                  </span>
                </div>
                {column.totalValue > 0 && (
                  <div className="text-lg font-bold text-success">
                    {formatValue(column.totalValue)}
                  </div>
                )}
              </div>

              {/* Column Content - Vertical scrolling */}
              <div className="h-[calc(100%-120px)]">
                <ScrollArea className="h-full">
                  <div className="space-y-4 pr-2">
                    {column.leads.map((lead) => (
                      <LeadCard
                        key={lead.id}
                        lead={lead}
                        onCall={onCall}
                        onEdit={onEdit}
                        onDelete={onDelete}
                        onWhatsApp={onWhatsApp}
                        isCallLoading={isCallLoading}
                        onRefresh={onRefresh}
                        hideStatus={true}
                      />
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};