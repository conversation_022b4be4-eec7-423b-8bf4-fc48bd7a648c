import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useLeadAnswerStatus, LeadAnswerStatus } from '@/hooks/useLeadAnswerStatus';
import { PhoneOff, RotateCcw } from 'lucide-react';

interface AnswerStatusManagerProps {
  leadId: string;
  initialAnswerStatus?: string;
  initialAttemptNumber?: number;
  onStatusUpdate?: (status: LeadAnswerStatus | null) => void;
}

export const AnswerStatusManager: React.FC<AnswerStatusManagerProps> = ({
  leadId,
  initialAnswerStatus,
  initialAttemptNumber,
  onStatusUpdate
}) => {
  const { 
    updateLeadAnswerStatus, 
    getLeadAnswerStatus, 
    getNextAttemptNumber,
    resetAnswerStatus 
  } = useLeadAnswerStatus();
  
  const [currentStatus, setCurrentStatus] = useState<LeadAnswerStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Use initial data if available, otherwise load from database
    if (initialAnswerStatus && initialAttemptNumber) {
      const initialStatus: LeadAnswerStatus = {
        id: '', // Not needed for display
        lead_id: leadId,
        company_id: '', // Not needed for display
        main_status: 'לא ענה',
        answer_status: initialAnswerStatus,
        attempt_number: initialAttemptNumber,
        created_at: '',
        updated_at: '',
        created_by: ''
      };
      setCurrentStatus(initialStatus);
      onStatusUpdate?.(initialStatus);
    } else {
      loadCurrentStatus();
    }
  }, [leadId, initialAnswerStatus, initialAttemptNumber]);

  const loadCurrentStatus = async () => {
    try {
      const status = await getLeadAnswerStatus(leadId);
      setCurrentStatus(status);
      onStatusUpdate?.(status);
    } catch (error) {
      console.error('Error loading answer status:', error);
    }
  };

  const handleNextAttempt = async () => {
    setIsLoading(true);
    try {
      const nextAttempt = await getNextAttemptNumber(leadId);
      if (nextAttempt <= 7) {
        const newStatus = await updateLeadAnswerStatus(leadId, nextAttempt);
        setCurrentStatus(newStatus);
        onStatusUpdate?.(newStatus);

        // Small delay to ensure database is updated before refresh
        setTimeout(() => {
          onStatusUpdate?.(newStatus);
        }, 100);
      }
    } catch (error) {
      console.error('Error updating answer status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = async () => {
    setIsLoading(true);
    try {
      await resetAnswerStatus(leadId);
      setCurrentStatus(null);
      onStatusUpdate?.(null);

      // Small delay to ensure database is updated before refresh
      setTimeout(() => {
        onStatusUpdate?.(null);
      }, 100);
    } catch (error) {
      console.error('Error resetting answer status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (attemptNumber: number) => {
    if (attemptNumber <= 2) return "bg-blue-100 text-blue-800 border-blue-200";
    if (attemptNumber <= 4) return "bg-orange-100 text-orange-800 border-orange-200";
    return "bg-red-100 text-red-800 border-red-200";
  };

  return (
    <div className="flex items-center gap-2">
      {currentStatus && (
        <Badge 
          variant="outline" 
          className={getStatusColor(currentStatus.attempt_number)}
        >
          {currentStatus.answer_status}
        </Badge>
      )}
      
      <div className="flex gap-1">
        {(!currentStatus || currentStatus.attempt_number < 7) && (
          <Button 
            size="sm" 
            variant="outline"
            onClick={handleNextAttempt}
            disabled={isLoading}
            className="flex items-center gap-1"
          >
            <PhoneOff className="w-3 h-3" />
            {!currentStatus 
              ? 'לא ענה' 
              : `ניסיון ${currentStatus.attempt_number + 1}`
            }
          </Button>
        )}
        
        {currentStatus && (
          <Button 
            size="sm" 
            variant="ghost"
            onClick={handleReset}
            disabled={isLoading}
            className="flex items-center gap-1 text-muted-foreground hover:text-foreground"
          >
            <RotateCcw className="w-3 h-3" />
            איפוס
          </Button>
        )}
      </div>
    </div>
  );
};
