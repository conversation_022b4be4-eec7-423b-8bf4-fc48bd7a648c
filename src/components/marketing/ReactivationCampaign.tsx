import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useLeads } from '@/hooks/useLeads';
import { useUserManagement } from '@/hooks/useUserManagement';
import { supabase } from '@/integrations/supabase/client';
import { useCompany } from '@/contexts/CompanyContext';
import { toast } from 'sonner';
import { 
  MessageSquare, 
  Users, 
  Send, 
  Calendar,
  Filter,
  User
} from 'lucide-react';

export const ReactivationCampaign = () => {
  const { leads } = useLeads();
  const { users } = useUserManagement();
  const { currentCompany } = useCompany();
  const [selectedLeads, setSelectedLeads] = useState<string[]>([]);
  const [isSending, setIsSending] = useState(false);

  // Filter leads that are suitable for reactivation
  const reactivationCandidates = leads.filter(lead => 
    lead.status !== 'לקוח סגור' && 
    lead.status !== 'לא מעוניין' &&
    lead.status !== 'לא מתאים'
  );

  const handleSelectLead = (leadId: string, checked: boolean) => {
    if (checked) {
      setSelectedLeads(prev => [...prev, leadId]);
    } else {
      setSelectedLeads(prev => prev.filter(id => id !== leadId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedLeads(reactivationCandidates.map(lead => lead.id));
    } else {
      setSelectedLeads([]);
    }
  };

  const sendReactivationCampaign = async () => {
    if (selectedLeads.length === 0) {
      toast.error('אנא בחר לפחות ליד אחד');
      return;
    }

    setIsSending(true);
    try {
      // Trigger reactivation workflow for each selected lead
      for (const leadId of selectedLeads) {
        await supabase.functions.invoke('workflow-executor', {
          body: {
            action: 'trigger',
            triggerData: {
              entity_type: 'lead',
              entity_id: leadId,
              trigger_type: 'manual_campaign',
              old_status: null,
              new_status: 'reactivation_campaign',
              company_id: currentCompany?.id
            }
          }
        });
      }

      toast.success(`קמפיין הפעלה נשלח ל-${selectedLeads.length} לידים`);
      setSelectedLeads([]);
    } catch (error) {
      console.error('Error sending reactivation campaign:', error);
      toast.error('שגיאה בשליחת קמפיין ההפעלה');
    } finally {
      setIsSending(false);
    }
  };

  const getAssignedUserName = (assignedUserId?: string) => {
    if (!assignedUserId) return 'לא מוקצה';
    const user = users.find(u => u.id === assignedUserId);
    return user?.full_name || user?.email || 'משתמש לא נמצא';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ליד חדש": return "bg-blue-100 text-blue-800 border-blue-200";
      case "צריך פולואפ": return "bg-orange-100 text-orange-800 border-orange-200";
      case "לא ענה": return "bg-purple-100 text-purple-800 border-purple-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            <CardTitle>קמפיין הפעלה חודשי</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="flex items-center gap-1">
              <Users className="w-3 h-3" />
              {reactivationCandidates.length} מועמדים
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <Filter className="w-3 h-3" />
              {selectedLeads.length} נבחרו
            </Badge>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          בחר לידים לשליחת הודעת הפעלה חודשית
        </p>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Checkbox
              checked={selectedLeads.length === reactivationCandidates.length && reactivationCandidates.length > 0}
              onCheckedChange={handleSelectAll}
            />
            <span className="text-sm">בחר הכל</span>
          </div>
          
          <Button
            onClick={sendReactivationCampaign}
            disabled={selectedLeads.length === 0 || isSending}
            className="flex items-center gap-2"
          >
            <Send className="w-4 h-4" />
            {isSending ? 'שולח...' : `שלח קמפיין (${selectedLeads.length})`}
          </Button>
        </div>

        {/* Leads List */}
        <ScrollArea className="h-[400px] border rounded-lg">
          <div className="p-4 space-y-3">
            {reactivationCandidates.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Calendar className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>אין לידים מתאימים לקמפיין הפעלה</p>
              </div>
            ) : (
              reactivationCandidates.map((lead) => (
                <div
                  key={lead.id}
                  className={`flex items-center gap-3 p-3 border rounded-lg transition-colors ${
                    selectedLeads.includes(lead.id) 
                      ? 'bg-primary/5 border-primary/20' 
                      : 'hover:bg-muted/50'
                  }`}
                >
                  <Checkbox
                    checked={selectedLeads.includes(lead.id)}
                    onCheckedChange={(checked) => handleSelectLead(lead.id, checked as boolean)}
                  />
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium truncate">{lead.full_name}</h4>
                      <Badge 
                        variant="outline" 
                        className={getStatusColor(lead.status)}
                      >
                        {lead.status}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>{lead.phone}</span>
                      {lead.email && <span>{lead.email}</span>}
                      <div className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        <span>{getAssignedUserName(lead.assigned_user_id)}</span>
                      </div>
                    </div>
                  </div>
                  
                  {lead.value && (
                    <div className="text-sm font-medium text-green-600">
                      ₪{lead.value.toLocaleString()}
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </ScrollArea>

        {/* Campaign Preview */}
        {selectedLeads.length > 0 && (
          <div className="bg-muted/30 p-4 rounded-lg">
            <h4 className="font-medium mb-2">תצוגה מקדימה של ההודעה:</h4>
            <div className="bg-background p-3 rounded border text-sm">
              "היי [שם הליד], כאן [שם המשתמש המוקצה]. אני רוצה לבדוק איתך אם יש לך צרכים משפטיים שאוכל לעזור בהם. אשמח לשמוע ממך!"
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              המשתנים יוחלפו אוטומטית עבור כל ליד
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
