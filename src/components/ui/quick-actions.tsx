import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { 
  Plus, 
  MessageSquare, 
  Phone, 
  Clock, 
  FolderOpen, 
  User,
  CheckCircle,
  Edit,
  Trash2
} from 'lucide-react';

interface QuickActionsProps {
  type: 'lead' | 'case' | 'dashboard';
  data?: any;
  onAction?: (action: string, data?: any) => void;
}

export const QuickActions: React.FC<QuickActionsProps> = ({ type, data, onAction }) => {
  const navigate = useNavigate();

  const handleAction = (action: string, additionalData?: any) => {
    if (onAction) {
      onAction(action, additionalData);
    }
  };

  if (type === 'dashboard') {
    return (
      <div className="flex gap-3">
        <Button 
          onClick={() => navigate('/office/leads')}
          className="flex items-center gap-2"
          variant="outline"
          size="sm"
        >
          <Plus className="w-4 h-4" />
          ליד חדש
        </Button>
        <Button 
          onClick={() => navigate('/cases')}
          className="flex items-center gap-2"
          variant="outline"
          size="sm"
        >
          <FolderOpen className="w-4 h-4" />
          תיק חדש
        </Button>
        <Button 
          onClick={() => navigate('/office/whatsapp')}
          className="flex items-center gap-2"
          variant="outline"
          size="sm"
        >
          <MessageSquare className="w-4 h-4" />
          וואטסאפ
        </Button>
      </div>
    );
  }

  if (type === 'lead' && data) {
    return (
      <div className="flex gap-1">
        <Button
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => handleAction('create-case', data)}
          title="צור תיק מליד"
        >
          <FolderOpen className="h-4 w-4" />
        </Button>
        {data.phone && (
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={() => window.open(`https://wa.me/${data.phone.replace(/[^0-9]/g, '')}`, '_blank')}
            title="שלח וואטסאפ"
          >
            <MessageSquare className="h-4 w-4" />
          </Button>
        )}
        {data.phone && (
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={() => window.open(`tel:${data.phone}`, '_self')}
            title="התקשר"
          >
            <Phone className="h-4 w-4" />
          </Button>
        )}
        <Button
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => handleAction('edit', data)}
          title="ערוך ליד"
        >
          <Edit className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  if (type === 'case' && data) {
    return (
      <div className="flex gap-1">
        <Button
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => navigate(`/case/${data.id}#time-tracking`)}
          title="רישום זמן"
        >
          <Clock className="h-4 w-4" />
        </Button>
        {data.lead?.phone && (
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={() => window.open(`https://wa.me/${data.lead.phone.replace(/[^0-9]/g, '')}`, '_blank')}
            title="שלח וואטסאפ"
          >
            <MessageSquare className="h-4 w-4" />
          </Button>
        )}
        {data.lead?.phone && (
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={() => window.open(`tel:${data.lead.phone}`, '_self')}
            title="התקשר ללקוח"
          >
            <Phone className="h-4 w-4" />
          </Button>
        )}
        {data.status !== 'סגור' && (
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={() => handleAction('close-case', data)}
            title="סגור תיק"
          >
            <CheckCircle className="h-4 w-4" />
          </Button>
        )}
        <Button
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0 text-destructive hover:text-destructive"
          onClick={() => handleAction('delete', data)}
          title="מחק תיק"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return null;
};

// Status Badge Component for consistent styling
interface StatusBadgeProps {
  status: string;
  onClick?: () => void;
  className?: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status, onClick, className = "" }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "ליד חדש": return "bg-blue-100 text-blue-800 border-blue-200";
      case "צריך פולואפ": return "bg-orange-100 text-orange-800 border-orange-200";
      case "לקוח סגור": return "bg-green-100 text-green-800 border-green-200";
      case "בקליטה": return "bg-purple-100 text-purple-800 border-purple-200";
      case "פתוח": return "bg-blue-100 text-blue-800 border-blue-200";
      case "סגור": return "bg-gray-100 text-gray-800 border-gray-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <span 
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(status)} ${onClick ? 'cursor-pointer hover:opacity-80' : ''} ${className}`}
      onClick={onClick}
    >
      {status}
    </span>
  );
};

// Quick Status Changer Component
interface QuickStatusChangerProps {
  currentStatus: string;
  availableStatuses: string[];
  onStatusChange: (newStatus: string) => void;
  type: 'lead' | 'case';
}

export const QuickStatusChanger: React.FC<QuickStatusChangerProps> = ({ 
  currentStatus, 
  availableStatuses, 
  onStatusChange, 
  type 
}) => {
  return (
    <div className="flex gap-1 flex-wrap">
      {availableStatuses
        .filter(status => status !== currentStatus)
        .map(status => (
          <Button
            key={status}
            size="sm"
            variant="outline"
            className="h-6 px-2 text-xs"
            onClick={() => onStatusChange(status)}
          >
            {status}
          </Button>
        ))
      }
    </div>
  );
};
