import { AreaChart as RechartsAreaChart, Area, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>ltip, Responsive<PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface AreaChartProps {
  title: string;
  data: any[];
  areas: {
    dataKey: string;
    color: string;
    name?: string;
  }[];
  xAxisKey: string;
  height?: number;
  showLegend?: boolean;
  stacked?: boolean;
}

export const AreaChart = ({ 
  title, 
  data, 
  areas,
  xAxisKey, 
  height = 300,
  showLegend = true,
  stacked = false 
}: AreaChartProps) => {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-foreground">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={height}>
          <RechartsAreaChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey={xAxisKey} 
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
            />
            <Tooltip 
              contentStyle={{
                backgroundColor: 'hsl(var(--background))',
                border: '1px solid hsl(var(--border))',
                borderRadius: '8px',
                color: 'hsl(var(--foreground))'
              }}
            />
            {showLegend && <Legend />}
            {areas.map((area, index) => (
              <Area 
                key={index}
                type="monotone" 
                dataKey={area.dataKey} 
                stackId={stacked ? "1" : undefined}
                stroke={area.color}
                fill={area.color}
                fillOpacity={0.6}
                name={area.name || area.dataKey}
              />
            ))}
          </RechartsAreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};
