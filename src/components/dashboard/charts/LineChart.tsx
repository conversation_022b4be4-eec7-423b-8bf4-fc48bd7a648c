import { LineChart as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Car<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Re<PERSON>onsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface LineChartProps {
  title: string;
  data: any[];
  lines: {
    dataKey: string;
    color: string;
    name?: string;
  }[];
  xAxisKey: string;
  height?: number;
  showLegend?: boolean;
}

export const LineChart = ({ 
  title, 
  data, 
  lines,
  xAxisKey, 
  height = 300,
  showLegend = true 
}: LineChartProps) => {
  return (
    <Card className="w-full border-0 shadow-lg hover:shadow-xl transition-all duration-300">
      <CardHeader className="bg-gradient-to-r from-muted/50 to-transparent">
        <CardTitle className="text-lg font-semibold text-foreground">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={height}>
          <RechartsLine<PERSON><PERSON> data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey={xAxisKey} 
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
            />
            <Tooltip 
              contentStyle={{
                backgroundColor: 'hsl(var(--background))',
                border: '1px solid hsl(var(--border))',
                borderRadius: '8px',
                color: 'hsl(var(--foreground))'
              }}
            />
            {showLegend && <Legend />}
            {lines.map((line, index) => (
              <Line 
                key={index}
                type="monotone" 
                dataKey={line.dataKey} 
                stroke={line.color}
                strokeWidth={2}
                dot={{ r: 4 }}
                name={line.name || line.dataKey}
              />
            ))}
          </RechartsLineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};
