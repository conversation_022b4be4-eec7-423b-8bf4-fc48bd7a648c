import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Lead } from "@/components/leads/LeadCard";
import { useUserManagement } from "@/hooks/useUserManagement";

interface LeadModalProps {
  isOpen: boolean;
  onClose: () => void;
  editingLead: Lead | null;
  onAddLead: (lead: Omit<Lead, 'id' | 'created_at' | 'updated_at'>) => Promise<Lead>;
  onUpdateLead: (leadId: string, updates: Partial<Lead>) => Promise<Lead>;
}

export const LeadModal = ({ isOpen, onClose, editingLead, onAddLead, onUpdateLead }: LeadModalProps) => {
  const { users } = useUserManagement();
  const [formData, setFormData] = useState({
    full_name: "",
    phone: "",
    email: "",
    source: "",
    status: "ליד חדש",
    value: "",
    notes: "",
    assigned_user_id: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (editingLead) {
      setFormData({
        full_name: editingLead.full_name || "",
        phone: editingLead.phone || "",
        email: editingLead.email || "",
        source: editingLead.source || "",
        status: editingLead.status || "ליד חדש",
        value: editingLead.value?.toString() || "",
        notes: editingLead.notes || "",
        assigned_user_id: editingLead.assigned_user_id || ""
      });
    } else {
      setFormData({
        full_name: "",
        phone: "",
        email: "",
        source: "",
        status: "ליד חדש",
        value: "",
        notes: "",
        assigned_user_id: ""
      });
    }
  }, [editingLead, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      const leadData = {
        full_name: formData.full_name,
        phone: formData.phone,
        email: formData.email || undefined,
        source: formData.source || undefined,
        status: formData.status,
        value: formData.value ? parseFloat(formData.value) : undefined,
        notes: formData.notes || undefined,
        assigned_user_id: formData.assigned_user_id || undefined
      };

      if (editingLead) {
        await onUpdateLead(editingLead.id, leadData);
      } else {
        await onAddLead(leadData);
      }
      
      onClose();
    } catch (error) {
      console.error('Error submitting lead:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const statusOptions = [
    "ליד חדש",
    "לא ענה", 
    "לא מעוניין",
    "לא מתאים",
    "צריך פולואפ",
    "לקוח סגור"
  ];

  const sourceOptions = [
    "גוגל",
    "פייסבוק",
    "המלצה",
    "אתר האינטרנט",
    "פרסום מודפס",
    "אחר"
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md hebrew-text" dir="rtl">
        <DialogHeader>
          <DialogTitle>
            {editingLead ? "עריכת ליד" : "הוספת ליד חדש"}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4 form-professional">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="full_name">שם מלא</Label>
              <Input
                id="full_name"
                value={formData.full_name}
                onChange={(e) => setFormData(prev => ({ ...prev, full_name: e.target.value }))}
                placeholder="הכנס שם מלא"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">טלפון</Label>
              <Input
                id="phone"
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="050-1234567"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="email">אימייל</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="value">שווי עסקה</Label>
              <Input
                id="value"
                type="number"
                value={formData.value}
                onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                placeholder="50000"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="assigned_user">משתמש מוקצה</Label>
            <Select value={formData.assigned_user_id} onValueChange={(value) => setFormData(prev => ({ ...prev, assigned_user_id: value === "none" ? "" : value }))}>
              <SelectTrigger>
                <SelectValue placeholder="בחר משתמש" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">לא מוקצה</SelectItem>
                {users.map((user) => (
                  <SelectItem key={user.id} value={user.id}>
                    {user.full_name || user.email}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="source">מקור</Label>
              <Select value={formData.source} onValueChange={(value) => setFormData(prev => ({ ...prev, source: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="בחר מקור" />
                </SelectTrigger>
                <SelectContent>
                  {sourceOptions.map((source) => (
                    <SelectItem key={source} value={source}>
                      {source}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="status">סטטוס</Label>
              <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="בחר סטטוס" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((status) => (
                    <SelectItem key={status} value={status}>
                      {status}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">הערות</Label>
            <Input
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              placeholder="הערות נוספות"
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
              ביטול
            </Button>
            <Button type="submit" className="btn-professional" disabled={isSubmitting}>
              {isSubmitting ? "שומר..." : editingLead ? "עדכן ליד" : "הוסף ליד"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};