import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Building2, User, Key, Settings, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { CompanyOnboardingData } from '@/types/company';
import { useSuperAdmin } from '@/hooks/useSuperAdmin';

const onboardingSchema = z.object({
  // Company Information
  companyName: z.string().min(2, 'Company name must be at least 2 characters'),
  companyEmail: z.string().email('Invalid email address'),
  companyPhone: z.string().min(10, 'Phone number must be at least 10 characters'),
  companyAddress: z.string().min(5, 'Address must be at least 5 characters'),
  subscriptionPlan: z.enum(['basic', 'pro', 'enterprise']),
  maxUsers: z.number().min(1).max(100),
  
  // Admin User Information
  adminFirstName: z.string().min(2, 'First name must be at least 2 characters'),
  adminLastName: z.string().min(2, 'Last name must be at least 2 characters'),
  adminEmail: z.string().email('Invalid email address'),
  adminPhone: z.string().min(10, 'Phone number must be at least 10 characters'),
  
  // API Tokens will be configured later in company settings
  
  // Additional Settings
  timezone: z.string().default('Asia/Jerusalem'),
  language: z.string().default('he'),
  notes: z.string().optional(),
});

type OnboardingFormData = z.infer<typeof onboardingSchema>;

interface CompanyOnboardingFormProps {
  onSuccess: () => void;
  onCancel: () => void;
}

export const CompanyOnboardingForm = ({ onSuccess, onCancel }: CompanyOnboardingFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { createCompanyWithAdmin } = useSuperAdmin();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm<OnboardingFormData>({
    resolver: zodResolver(onboardingSchema),
    defaultValues: {
      subscriptionPlan: 'basic',
      maxUsers: 10,
      timezone: 'Asia/Jerusalem',
      language: 'he'
    }
  });

  const onSubmit = async (data: OnboardingFormData) => {
    setIsSubmitting(true);
    try {
      await createCompanyWithAdmin(data);
      onSuccess();
    } catch (error) {
      console.error('Onboarding error:', error);
      toast.error('Failed to onboard company. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Company Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Company Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="companyName">Company Name *</Label>
              <Input
                id="companyName"
                {...register('companyName')}
                placeholder="Legal Firm Ltd."
              />
              {errors.companyName && (
                <p className="text-sm text-red-600">{errors.companyName.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="companyEmail">Company Email *</Label>
              <Input
                id="companyEmail"
                type="email"
                {...register('companyEmail')}
                placeholder="<EMAIL>"
              />
              {errors.companyEmail && (
                <p className="text-sm text-red-600">{errors.companyEmail.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="companyPhone">Company Phone *</Label>
              <Input
                id="companyPhone"
                {...register('companyPhone')}
                placeholder="+972-50-123-4567"
              />
              {errors.companyPhone && (
                <p className="text-sm text-red-600">{errors.companyPhone.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="subscriptionPlan">Subscription Plan *</Label>
              <Select
                value={watch('subscriptionPlan')}
                onValueChange={(value) => setValue('subscriptionPlan', value as any)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="basic">Basic (₪299/month)</SelectItem>
                  <SelectItem value="pro">Pro (₪599/month)</SelectItem>
                  <SelectItem value="enterprise">Enterprise (₪999/month)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="companyAddress">Company Address *</Label>
            <Textarea
              id="companyAddress"
              {...register('companyAddress')}
              placeholder="123 Main Street, Tel Aviv, Israel"
              rows={2}
            />
            {errors.companyAddress && (
              <p className="text-sm text-red-600">{errors.companyAddress.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="maxUsers">Maximum Users</Label>
            <Input
              id="maxUsers"
              type="number"
              {...register('maxUsers', { valueAsNumber: true })}
              min={1}
              max={100}
            />
            {errors.maxUsers && (
              <p className="text-sm text-red-600">{errors.maxUsers.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Admin User Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Admin User Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="adminFirstName">First Name *</Label>
              <Input
                id="adminFirstName"
                {...register('adminFirstName')}
                placeholder="John"
              />
              {errors.adminFirstName && (
                <p className="text-sm text-red-600">{errors.adminFirstName.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="adminLastName">Last Name *</Label>
              <Input
                id="adminLastName"
                {...register('adminLastName')}
                placeholder="Doe"
              />
              {errors.adminLastName && (
                <p className="text-sm text-red-600">{errors.adminLastName.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="adminEmail">Admin Email *</Label>
              <Input
                id="adminEmail"
                type="email"
                {...register('adminEmail')}
                placeholder="<EMAIL>"
              />
              {errors.adminEmail && (
                <p className="text-sm text-red-600">{errors.adminEmail.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="adminPhone">Admin Phone *</Label>
              <Input
                id="adminPhone"
                {...register('adminPhone')}
                placeholder="+972-50-123-4567"
              />
              {errors.adminPhone && (
                <p className="text-sm text-red-600">{errors.adminPhone.message}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Note about API configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            API Integration Setup
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm text-blue-800">
              <strong>Note:</strong> API tokens for WhatsApp (GreenAPI) and Twilio will be configured
              after company creation through the Company Settings page. This ensures secure token management
              and allows the company admin to configure integrations when ready.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Additional Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Additional Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="timezone">Timezone</Label>
              <Select
                value={watch('timezone')}
                onValueChange={(value) => setValue('timezone', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Asia/Jerusalem">Asia/Jerusalem (GMT+2)</SelectItem>
                  <SelectItem value="Europe/London">Europe/London (GMT+0)</SelectItem>
                  <SelectItem value="America/New_York">America/New_York (GMT-5)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="language">Language</Label>
              <Select
                value={watch('language')}
                onValueChange={(value) => setValue('language', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="he">Hebrew</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              {...register('notes')}
              placeholder="Any additional notes about this company..."
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex items-center justify-end gap-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating Company...
            </>
          ) : (
            'Create Company & Admin User'
          )}
        </Button>
      </div>
    </form>
  );
};
