import React, { useState, useEffect } from 'react';
import { Card, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  Database, 
  Clock, 
  MemoryStick, 
  Zap, 
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';
import { useQueryClient } from '@tanstack/react-query';
import { useDashboardPerformance } from '@/hooks/useOptimizedDashboard';

interface PerformanceMetrics {
  queryCount: number;
  cacheHitRate: number;
  averageQueryTime: number;
  memoryUsage: number;
  activeConnections: number;
  errorRate: number;
}

export const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    queryCount: 0,
    cacheHitRate: 0,
    averageQueryTime: 0,
    memoryUsage: 0,
    activeConnections: 0,
    errorRate: 0
  });
  const [isMonitoring, setIsMonitoring] = useState(false);
  
  const queryClient = useQueryClient();
  const { getPerformanceMetrics } = useDashboardPerformance();

  // Collect performance metrics
  const collectMetrics = () => {
    try {
      // React Query metrics
      const queryMetrics = getPerformanceMetrics();
      
      // Browser memory metrics
      const memoryInfo = (performance as any).memory;
      const memoryUsage = memoryInfo ? 
        (memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100 : 0;

      // Query cache metrics
      const cache = queryClient.getQueryCache();
      const queries = cache.getAll();
      const errorQueries = queries.filter(q => q.state.error);
      
      setMetrics({
        queryCount: queries.length,
        cacheHitRate: queryMetrics.cacheHitRate * 100,
        averageQueryTime: queryMetrics.averageQueryTime,
        memoryUsage,
        activeConnections: queries.filter(q => q.state.fetchStatus === 'fetching').length,
        errorRate: queries.length > 0 ? (errorQueries.length / queries.length) * 100 : 0
      });
    } catch (error) {
      console.error('Error collecting performance metrics:', error);
    }
  };

  // Auto-refresh metrics
  useEffect(() => {
    if (isMonitoring) {
      const interval = setInterval(collectMetrics, 2000); // Every 2 seconds
      return () => clearInterval(interval);
    }
  }, [isMonitoring]);

  // Initial metrics collection
  useEffect(() => {
    collectMetrics();
  }, []);

  const getStatusColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.warning) return 'text-orange-600';
    return 'text-red-600';
  };

  const getStatusIcon = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return <CheckCircle className="w-4 h-4 text-green-600" />;
    if (value <= thresholds.warning) return <AlertTriangle className="w-4 h-4 text-orange-600" />;
    return <AlertTriangle className="w-4 h-4 text-red-600" />;
  };

  const clearCache = () => {
    queryClient.clear();
    collectMetrics();
  };

  const invalidateAll = () => {
    queryClient.invalidateQueries();
    collectMetrics();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Performance Monitor</h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsMonitoring(!isMonitoring)}
          >
            <Activity className="w-4 h-4 mr-2" />
            {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
          </Button>
          <Button variant="outline" size="sm" onClick={collectMetrics}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Query Performance */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Query Performance</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Active Queries</span>
                <Badge variant="outline">{metrics.queryCount}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Cache Hit Rate</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(100 - metrics.cacheHitRate, { good: 20, warning: 50 })}
                  <span className={getStatusColor(100 - metrics.cacheHitRate, { good: 20, warning: 50 })}>
                    {metrics.cacheHitRate.toFixed(1)}%
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Avg Query Time</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(metrics.averageQueryTime, { good: 500, warning: 1000 })}
                  <span className={getStatusColor(metrics.averageQueryTime, { good: 500, warning: 1000 })}>
                    {metrics.averageQueryTime.toFixed(0)}ms
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Memory Usage */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
            <MemoryStick className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Heap Usage</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(metrics.memoryUsage, { good: 50, warning: 80 })}
                  <span className={getStatusColor(metrics.memoryUsage, { good: 50, warning: 80 })}>
                    {metrics.memoryUsage.toFixed(1)}%
                  </span>
                </div>
              </div>
              <Progress 
                value={metrics.memoryUsage} 
                className="h-2"
              />
              <div className="text-xs text-muted-foreground">
                {metrics.memoryUsage > 80 ? 'High memory usage detected' : 'Memory usage normal'}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Connection Status */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Connection Status</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Active Connections</span>
                <Badge variant="outline">{metrics.activeConnections}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Error Rate</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(metrics.errorRate, { good: 1, warning: 5 })}
                  <span className={getStatusColor(metrics.errorRate, { good: 1, warning: 5 })}>
                    {metrics.errorRate.toFixed(1)}%
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Status</span>
                <Badge variant={metrics.errorRate < 5 ? "default" : "destructive"}>
                  {metrics.errorRate < 5 ? "Healthy" : "Issues"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Performance Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {metrics.cacheHitRate < 70 && (
              <div className="flex items-start gap-3 p-3 bg-orange-50 rounded-lg">
                <AlertTriangle className="w-5 h-5 text-orange-600 mt-0.5" />
                <div>
                  <p className="font-medium text-orange-800">Low Cache Hit Rate</p>
                  <p className="text-sm text-orange-700">
                    Consider increasing staleTime for frequently accessed data.
                  </p>
                </div>
              </div>
            )}
            
            {metrics.averageQueryTime > 1000 && (
              <div className="flex items-start gap-3 p-3 bg-red-50 rounded-lg">
                <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
                <div>
                  <p className="font-medium text-red-800">Slow Query Performance</p>
                  <p className="text-sm text-red-700">
                    Database queries are taking longer than expected. Check indexes and query optimization.
                  </p>
                </div>
              </div>
            )}
            
            {metrics.memoryUsage > 80 && (
              <div className="flex items-start gap-3 p-3 bg-red-50 rounded-lg">
                <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
                <div>
                  <p className="font-medium text-red-800">High Memory Usage</p>
                  <p className="text-sm text-red-700">
                    Memory usage is high. Consider clearing cache or reducing data retention.
                  </p>
                  <Button variant="outline" size="sm" onClick={clearCache} className="mt-2">
                    Clear Cache
                  </Button>
                </div>
              </div>
            )}
            
            {metrics.errorRate > 5 && (
              <div className="flex items-start gap-3 p-3 bg-red-50 rounded-lg">
                <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
                <div>
                  <p className="font-medium text-red-800">High Error Rate</p>
                  <p className="text-sm text-red-700">
                    Multiple queries are failing. Check network connectivity and API status.
                  </p>
                  <Button variant="outline" size="sm" onClick={invalidateAll} className="mt-2">
                    Retry Failed Queries
                  </Button>
                </div>
              </div>
            )}
            
            {metrics.cacheHitRate >= 70 && 
             metrics.averageQueryTime <= 1000 && 
             metrics.memoryUsage <= 80 && 
             metrics.errorRate <= 5 && (
              <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium text-green-800">Performance Optimal</p>
                  <p className="text-sm text-green-700">
                    All performance metrics are within acceptable ranges.
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
