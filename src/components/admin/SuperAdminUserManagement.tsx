import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Users,
  Plus,
  MoreHorizontal,
  Shield,
  User,
  Mail,
  Building2,
  UserCheck,
  UserX,
  Search,
  Filter,
  Database
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAuth } from '@/hooks/useAuth';
import { UserSyncUtilities } from './UserSyncUtilities';

export interface AllSystemUser {
  id: string;
  email: string;
  full_name?: string;
  role: 'super_admin' | 'company_admin' | 'user';
  company_id?: string;
  company_name?: string;
  created_at: string;
  last_sign_in_at?: string;
  is_active: boolean;
  status: 'active' | 'pending' | 'expired';
  invitation_id?: string;
}

export const SuperAdminUserManagement: React.FC = () => {
  const { isSuperAdmin } = useAuth();
  const [users, setUsers] = useState<AllSystemUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [companyFilter, setCompanyFilter] = useState<string>('all');
  const [companies, setCompanies] = useState<Array<{id: string, name: string}>>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<AllSystemUser | null>(null);
  const [newUser, setNewUser] = useState({
    email: '',
    full_name: '',
    role: 'user' as 'company_admin' | 'user',
    company_id: '',
    password: ''
  });

  // Fetch all users across all companies
  const fetchAllUsers = async () => {
    if (!isSuperAdmin) return;

    try {
      setIsLoading(true);

      // Fetch companies first
      const { data: companiesData, error: companiesError } = await supabase
        .from('companies')
        .select('id, name')
        .order('name');

      if (companiesError) throw companiesError;
      setCompanies(companiesData || []);

      // Fetch all user roles with company information and user details
      const { data: userRoles, error: rolesError } = await supabase
        .from('user_roles')
        .select(`
          user_id,
          role,
          company_id,
          created_at,
          companies(name)
        `)
        .order('created_at', { ascending: false });

      if (rolesError) throw rolesError;

      // Fetch user details from auth.users for each user
      const userIds = userRoles?.map(ur => ur.user_id) || [];
      let authUsers: any[] = [];

      if (userIds.length > 0) {
        // Use RPC function to get user details (since we can't directly query auth.users from client)
        const { data: authUsersData, error: authError } = await supabase.rpc('get_users_by_ids', {
          user_ids: userIds
        });

        if (authError) {
          console.warn('Could not fetch auth user details:', authError);
          // Continue with placeholder data if RPC fails
        } else {
          authUsers = authUsersData || [];
        }
      }

      // Transform active users with real auth data
      const activeUsers: AllSystemUser[] = userRoles?.map(userRole => {
        const authUser = authUsers.find(au => au.id === userRole.user_id);
        return {
          id: userRole.user_id,
          email: authUser?.email || `user-${userRole.user_id.slice(0, 8)}@system.com`,
          full_name: authUser?.raw_user_meta_data?.full_name ||
                    `${authUser?.raw_user_meta_data?.first_name || ''} ${authUser?.raw_user_meta_data?.last_name || ''}`.trim() ||
                    authUser?.email?.split('@')[0] || // Use email prefix as fallback
                    'Unknown User',
          role: userRole.role as 'super_admin' | 'company_admin' | 'user',
          company_id: userRole.company_id,
          company_name: userRole.companies?.name || 'Unknown Company',
          created_at: userRole.created_at,
          last_sign_in_at: authUser?.last_sign_in_at,
          is_active: true,
          status: 'active'
        };
      }) || [];

      setUsers(activeUsers);
    } catch (error) {
      console.error('Error fetching all users:', error);
      toast.error('Failed to load users');
    } finally {
      setIsLoading(false);
    }
  };

  // Create new user directly with password
  const createUser = async () => {
    if (!newUser.email || !newUser.full_name || !newUser.company_id || !newUser.password) {
      toast.error('Please fill in all required fields including password');
      return;
    }

    if (newUser.password.length < 6) {
      toast.error('Password must be at least 6 characters long');
      return;
    }

    try {
      // Create user directly using Supabase Edge Function
      const { data: createUserResult, error: createUserError } = await supabase.functions.invoke('create-user-with-password', {
        body: {
          email: newUser.email,
          password: newUser.password,
          fullName: newUser.full_name,
          role: newUser.role,
          companyId: newUser.company_id
        }
      });

      if (createUserError) throw createUserError;

      if (!createUserResult?.success) {
        throw new Error(createUserResult?.error || 'Failed to create user');
      }

      toast.success(`User created successfully! They can now login with email: ${newUser.email}`);

      setIsCreateDialogOpen(false);
      setNewUser({
        email: '',
        full_name: '',
        role: 'user',
        company_id: '',
        password: ''
      });

      await fetchAllUsers();
    } catch (error: any) {
      console.error('Error creating user:', error);
      if (error.message?.includes('already registered') || error.message?.includes('already exists')) {
        toast.error('A user with this email already exists');
      } else {
        toast.error(error.message || 'Failed to create user');
      }
    }
  };

  // Update user details
  const updateUser = async (userId: string, updates: Partial<AllSystemUser>) => {
    try {
      // Update user role if changed
      if (updates.role && updates.company_id) {
        const { error: roleError } = await supabase
          .from('user_roles')
          .update({
            role: updates.role,
            company_id: updates.company_id
          })
          .eq('user_id', userId);

        if (roleError) throw roleError;
      }

      toast.success('User updated successfully!');
      setIsEditDialogOpen(false);
      setEditingUser(null);
      fetchAllUsers(); // Refresh the list

    } catch (error: any) {
      console.error('Error updating user:', error);
      toast.error(error.message || 'Failed to update user');
    }
  };

  // Delete user completely (from both auth and user_roles)
  const deleteUser = async (userId: string, userStatus: string) => {
    try {
      // Use the new user-management function to delete completely
      const { data: deleteResult, error: deleteError } = await supabase.functions.invoke('user-management', {
        body: {
          action: 'delete',
          userId: userId
        }
      });

      if (deleteError) throw deleteError;

      if (!deleteResult?.success) {
        throw new Error(deleteResult?.error || 'Failed to delete user');
      }

      toast.success('User deleted completely! They can now be recreated if needed.');
      fetchAllUsers(); // Refresh the list

    } catch (error: any) {
      console.error('Error deleting user:', error);
      toast.error(error.message || 'Failed to delete user');
    }
  };

  // Send password reset
  const sendPasswordReset = async (email: string) => {
    try {
      // Try the built-in Supabase method first
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });

      if (error) {
        // If SMTP is not configured, provide helpful instructions
        if (error.message.includes('SMTP') || error.message.includes('email')) {
          toast.error(
            'Email not configured. Please set up SMTP in Supabase dashboard or manually send reset link.',
            { duration: 8000 }
          );

          // Show instructions dialog
          const resetUrl = `${window.location.origin}/reset-password`;
          const instructions = `
Email configuration needed! To send password resets:

1. Go to Supabase Dashboard → Authentication → Settings
2. Configure SMTP settings with your email provider
3. Or manually send this reset link to ${email}:

${resetUrl}

Alternative: Use Supabase Dashboard → Authentication → Users → Find user → Send password reset
          `;

          // Copy instructions to clipboard
          navigator.clipboard.writeText(instructions).then(() => {
            toast.info('Instructions copied to clipboard!', { duration: 5000 });
          });

          return;
        }
        throw error;
      }

      toast.success('Password reset email sent successfully!');
    } catch (error: any) {
      console.error('Error sending password reset:', error);
      toast.error(error.message || 'Failed to send password reset');
    }
  };

  // Filter users based on search and filters
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.company_name?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    const matchesCompany = companyFilter === 'all' || user.company_id === companyFilter;
    
    return matchesSearch && matchesRole && matchesCompany;
  });

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'super_admin':
        return (
          <Badge variant="default" className="bg-purple-100 text-purple-800">
            <Shield className="w-3 h-3 mr-1" />
            Super Admin
          </Badge>
        );
      case 'company_admin':
        return (
          <Badge variant="default" className="bg-blue-100 text-blue-800">
            <Shield className="w-3 h-3 mr-1" />
            Company Admin
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            <User className="w-3 h-3 mr-1" />
            User
          </Badge>
        );
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
      case 'pending':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Pending</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  useEffect(() => {
    if (isSuperAdmin) {
      fetchAllUsers();
    }
  }, [isSuperAdmin]);

  if (!isSuperAdmin) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-muted-foreground">
            Access denied. Super admin privileges required.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">System User Management</h2>
          <p className="text-muted-foreground">
            Manage all users and synchronization across all companies
          </p>
        </div>
      </div>

      <Tabs defaultValue="users" className="space-y-6">
        <TabsList>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            All Users
          </TabsTrigger>
          <TabsTrigger value="sync" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            User Sync
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold">All System Users</h3>
              <p className="text-sm text-muted-foreground">
                Manage all users across all companies
              </p>
            </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create User
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Create New User</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="full_name">Full Name</Label>
                <Input
                  id="full_name"
                  value={newUser.full_name}
                  onChange={(e) => setNewUser(prev => ({ ...prev, full_name: e.target.value }))}
                  placeholder="John Doe"
                />
              </div>
              <div>
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={newUser.password}
                  onChange={(e) => setNewUser(prev => ({ ...prev, password: e.target.value }))}
                  placeholder="Enter password (min 6 characters)"
                  minLength={6}
                />
              </div>
              <div>
                <Label htmlFor="company">Company</Label>
                <Select value={newUser.company_id} onValueChange={(value) => setNewUser(prev => ({ ...prev, company_id: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select company" />
                  </SelectTrigger>
                  <SelectContent>
                    {companies.map((company) => (
                      <SelectItem key={company.id} value={company.id}>
                        {company.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="role">Role</Label>
                <Select value={newUser.role} onValueChange={(value: 'company_admin' | 'user') => setNewUser(prev => ({ ...prev, role: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">User</SelectItem>
                    <SelectItem value="company_admin">Company Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex gap-2">
                <Button onClick={createUser} className="flex-1">
                  Create User
                </Button>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Edit User Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Edit User</DialogTitle>
            </DialogHeader>
            {editingUser && (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="edit-email">Email Address</Label>
                  <Input
                    id="edit-email"
                    type="email"
                    value={editingUser.email}
                    onChange={(e) => setEditingUser({...editingUser, email: e.target.value})}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="edit-name">Full Name</Label>
                  <Input
                    id="edit-name"
                    value={editingUser.full_name || ''}
                    onChange={(e) => setEditingUser({...editingUser, full_name: e.target.value})}
                    placeholder="John Doe"
                  />
                </div>
                <div>
                  <Label htmlFor="edit-company">Company</Label>
                  <Select
                    value={editingUser.company_id || ''}
                    onValueChange={(value) => setEditingUser({...editingUser, company_id: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select company" />
                    </SelectTrigger>
                    <SelectContent>
                      {companies.map((company) => (
                        <SelectItem key={company.id} value={company.id}>
                          {company.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-role">Role</Label>
                  <Select
                    value={editingUser.role}
                    onValueChange={(value) => setEditingUser({...editingUser, role: value as 'super_admin' | 'company_admin' | 'user'})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="super_admin">Super Admin</SelectItem>
                      <SelectItem value="company_admin">Company Admin</SelectItem>
                      <SelectItem value="user">User</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={() => updateUser(editingUser.id, editingUser)}
                    className="flex-1"
                  >
                    Update User
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsEditDialogOpen(false);
                      setEditingUser(null);
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search users, emails, or companies..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="super_admin">Super Admin</SelectItem>
                  <SelectItem value="company_admin">Company Admin</SelectItem>
                  <SelectItem value="user">User</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={companyFilter} onValueChange={setCompanyFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by company" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Companies</SelectItem>
                  {companies.map((company) => (
                    <SelectItem key={company.id} value={company.id}>
                      {company.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            System Users ({filteredUsers.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">Loading users...</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Company</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{user.full_name || 'Unknown'}</div>
                        <div className="text-sm text-muted-foreground">{user.email}</div>
                      </div>
                    </TableCell>
                    <TableCell>{getRoleBadge(user.role)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        {user.company_name || 'No Company'}
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(user.status)}</TableCell>
                    <TableCell>
                      {new Date(user.created_at).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => {
                              setEditingUser(user);
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <User className="h-4 w-4 mr-2" />
                            Edit User
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => sendPasswordReset(user.email)}
                          >
                            <Mail className="h-4 w-4 mr-2" />
                            Send Password Reset
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => deleteUser(user.id, user.status)}
                          >
                            <UserX className="h-4 w-4 mr-2" />
                            Remove User
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
        </TabsContent>

        <TabsContent value="sync" className="space-y-6">
          <UserSyncUtilities />
        </TabsContent>
      </Tabs>
    </div>
  );
};
