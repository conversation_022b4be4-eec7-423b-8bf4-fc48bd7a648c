import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, DialogTrigger, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>L<PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Building2, 
  Users, 
  Settings, 
  Trash2, 
  Edit, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  DollarSign,
  Shield,
  User,
  Plus,
  ArrowLeft
} from 'lucide-react';
import { useUserManagement } from '@/hooks/useUserManagement';
import { useCompany } from '@/contexts/CompanyContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface Company {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  status?: string;
  subscription_plan?: string;
  max_users?: number;
  created_at: string;
  updated_at: string;
}

interface CompanyManagementProps {
  company: Company;
  onClose: () => void;
  onUpdate: () => void;
}

export const CompanyManagement: React.FC<CompanyManagementProps> = ({
  company,
  onClose,
  onUpdate
}) => {
  const { handleCompanyUpdated, handleCompanyDeleted } = useCompany();
  const [activeTab, setActiveTab] = useState('details');
  const [isEditing, setIsEditing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [editedCompany, setEditedCompany] = useState<Company>(company);
  
  // Set the company context for user management
  const { setSelectedCompany } = useCompany();
  React.useEffect(() => {
    setSelectedCompany(company);
  }, [company, setSelectedCompany]);

  const {
    users,
    isLoading: usersLoading,
    createUser,
    updateUserRole,
    deactivateUser,
    sendPasswordReset,
    getUserStats
  } = useUserManagement();

  const stats = getUserStats();

  const handleSaveCompany = async () => {
    try {
      const { error } = await supabase
        .from('companies')
        .update({
          name: editedCompany.name,
          email: editedCompany.email,
          phone: editedCompany.phone,
          address: editedCompany.address,
          status: editedCompany.status,
          subscription_plan: editedCompany.subscription_plan,
          max_users: editedCompany.max_users,
          updated_at: new Date().toISOString()
        })
        .eq('id', company.id);

      if (error) throw error;

      toast.success('פרטי החברה עודכנו בהצלחה');
      setIsEditing(false);

      // Use CompanyContext to handle the update
      await handleCompanyUpdated(editedCompany);
      onUpdate();
    } catch (error) {
      console.error('Error updating company:', error);
      toast.error('שגיאה בעדכון פרטי החברה');
    }
  };

  const handleDeleteCompany = async () => {
    try {
      setIsDeleting(true);

      // Delete company (cascade will handle related data)
      const { error } = await supabase
        .from('companies')
        .delete()
        .eq('id', company.id);

      if (error) throw error;

      toast.success('החברה נמחקה בהצלחה');

      // Use CompanyContext to handle the deletion
      await handleCompanyDeleted(company.id);
      onUpdate();
      onClose();
    } catch (error: any) {
      console.error('Error deleting company:', error);
      toast.error(`שגיאה במחיקת החברה: ${error.message || 'שגיאה לא ידועה'}`);
    } finally {
      setIsDeleting(false);
    }
  };

  const getStatusBadge = (status?: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">פעיל</Badge>;
      case 'inactive':
        return <Badge variant="destructive">לא פעיל</Badge>;
      case 'suspended':
        return <Badge variant="outline" className="bg-orange-100 text-orange-800">מושעה</Badge>;
      default:
        return <Badge variant="secondary">לא ידוע</Badge>;
    }
  };

  const getPlanBadge = (plan?: string) => {
    switch (plan) {
      case 'premium':
        return <Badge variant="default" className="bg-purple-100 text-purple-800">פרימיום</Badge>;
      case 'basic':
        return <Badge variant="outline">בסיסי</Badge>;
      case 'enterprise':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">ארגוני</Badge>;
      default:
        return <Badge variant="secondary">ללא תוכנית</Badge>;
    }
  };

  const getRoleBadge = (role: string) => {
    if (role === 'super_admin') {
      return (
        <Badge variant="default" className="bg-purple-100 text-purple-800">
          <Shield className="w-3 h-3 mr-1" />
          סופר אדמין
        </Badge>
      );
    }
    return role === 'company_admin' ? (
      <Badge variant="default" className="bg-blue-100 text-blue-800">
        <Shield className="w-3 h-3 mr-1" />
        מנהל
      </Badge>
    ) : (
      <Badge variant="secondary">
        <User className="w-3 h-3 mr-1" />
        משתמש
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={onClose}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            חזור לרשימת החברות
          </Button>
          <div>
            <h1 className="text-2xl font-bold" dir="rtl">ניהול חברה: {company.name}</h1>
            <p className="text-muted-foreground" dir="rtl">
              נוצר ב-{new Date(company.created_at).toLocaleDateString('he-IL')}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getStatusBadge(company.status)}
          {getPlanBadge(company.subscription_plan)}
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="details">פרטי החברה</TabsTrigger>
          <TabsTrigger value="users">משתמשים ({stats.total})</TabsTrigger>
          <TabsTrigger value="settings">הגדרות</TabsTrigger>
        </TabsList>

        {/* Company Details Tab */}
        <TabsContent value="details" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2" dir="rtl">
                  <Building2 className="h-5 w-5" />
                  פרטי החברה
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditing(!isEditing)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  {isEditing ? 'ביטול' : 'עריכה'}
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {isEditing ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">שם החברה</Label>
                      <Input
                        id="name"
                        value={editedCompany.name}
                        onChange={(e) => setEditedCompany(prev => ({ ...prev, name: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">אימייל</Label>
                      <Input
                        id="email"
                        type="email"
                        value={editedCompany.email || ''}
                        onChange={(e) => setEditedCompany(prev => ({ ...prev, email: e.target.value }))}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="phone">טלפון</Label>
                      <Input
                        id="phone"
                        value={editedCompany.phone || ''}
                        onChange={(e) => setEditedCompany(prev => ({ ...prev, phone: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="max_users">מספר משתמשים מקסימלי</Label>
                      <Input
                        id="max_users"
                        type="number"
                        value={editedCompany.max_users || ''}
                        onChange={(e) => setEditedCompany(prev => ({ ...prev, max_users: parseInt(e.target.value) || undefined }))}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="address">כתובת</Label>
                    <Textarea
                      id="address"
                      value={editedCompany.address || ''}
                      onChange={(e) => setEditedCompany(prev => ({ ...prev, address: e.target.value }))}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="status">סטטוס</Label>
                      <Select value={editedCompany.status || ''} onValueChange={(value) => setEditedCompany(prev => ({ ...prev, status: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="active">פעיל</SelectItem>
                          <SelectItem value="inactive">לא פעיל</SelectItem>
                          <SelectItem value="suspended">מושעה</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="plan">תוכנית מנוי</Label>
                      <Select value={editedCompany.subscription_plan || ''} onValueChange={(value) => setEditedCompany(prev => ({ ...prev, subscription_plan: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="basic">בסיסי</SelectItem>
                          <SelectItem value="premium">פרימיום</SelectItem>
                          <SelectItem value="enterprise">ארגוני</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleSaveCompany}>
                      שמור שינויים
                    </Button>
                    <Button variant="outline" onClick={() => {
                      setEditedCompany(company);
                      setIsEditing(false);
                    }}>
                      ביטול
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">אימייל:</span>
                        <span>{company.email || 'לא צוין'}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">טלפון:</span>
                        <span>{company.phone || 'לא צוין'}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">מספר משתמשים מקסימלי:</span>
                        <span>{company.max_users || 'ללא הגבלה'}</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">נוצר:</span>
                        <span>{new Date(company.created_at).toLocaleDateString('he-IL')}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">עודכן:</span>
                        <span>{new Date(company.updated_at).toLocaleDateString('he-IL')}</span>
                      </div>
                    </div>
                  </div>
                  {company.address && (
                    <div className="flex items-start gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground mt-1" />
                      <div>
                        <span className="font-medium">כתובת:</span>
                        <p className="text-sm text-muted-foreground mt-1">{company.address}</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-6">
          {/* User Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-primary">{stats.total}</div>
              <div className="text-sm text-muted-foreground">סה"כ משתמשים</div>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-green-600">{stats.active}</div>
              <div className="text-sm text-muted-foreground">פעילים</div>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.admins}</div>
              <div className="text-sm text-muted-foreground">מנהלים</div>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{stats.users}</div>
              <div className="text-sm text-muted-foreground">משתמשים רגילים</div>
            </div>
          </div>

          {/* Users List */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2" dir="rtl">
                <Users className="h-5 w-5" />
                רשימת משתמשים
              </CardTitle>
            </CardHeader>
            <CardContent>
              {usersLoading ? (
                <div className="text-center py-6">טוען...</div>
              ) : users.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground" dir="rtl">
                  <Users className="h-8 w-8 mx-auto mb-2" />
                  <p>אין משתמשים רשומים</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {users.map((user) => (
                    <div key={user.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div>
                          <p className="font-medium">{user.full_name || 'לא צוין'}</p>
                          <p className="text-sm text-muted-foreground">{user.email}</p>
                        </div>
                        {getRoleBadge(user.role)}
                        {user.status === 'pending' ? (
                          <Badge variant="outline" className="bg-yellow-100 text-yellow-800">ממתין לאישור</Badge>
                        ) : user.is_active ? (
                          <Badge variant="default" className="bg-green-100 text-green-800">פעיל</Badge>
                        ) : (
                          <Badge variant="destructive">לא פעיל</Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        {user.status !== 'pending' && user.role !== 'super_admin' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateUserRole(user.id, user.role === 'company_admin' ? 'user' : 'company_admin')}
                          >
                            {user.role === 'company_admin' ? 'הפוך למשתמש' : 'הפוך למנהל'}
                          </Button>
                        )}
                        {user.status !== 'pending' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => sendPasswordReset(user.email)}
                          >
                            איפוס סיסמה
                          </Button>
                        )}
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => deactivateUser(user.id)}
                        >
                          {user.status === 'pending' ? 'בטל הזמנה' : 'הסר'}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-destructive" dir="rtl">
                <Trash2 className="h-5 w-5" />
                אזור מסוכן
              </CardTitle>
              <CardDescription dir="rtl">
                פעולות בלתי הפיכות שיכולות להשפיע על החברה
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive">
                    <Trash2 className="h-4 w-4 mr-2" />
                    מחק חברה
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>האם אתה בטוח?</AlertDialogTitle>
                    <AlertDialogDescription>
                      פעולה זו תמחק את החברה "{company.name}" ואת כל הנתונים הקשורים אליה.
                      פעולה זו בלתי הפיכה.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>ביטול</AlertDialogCancel>
                    <AlertDialogAction 
                      onClick={handleDeleteCompany}
                      disabled={isDeleting}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      {isDeleting ? 'מוחק...' : 'מחק חברה'}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
