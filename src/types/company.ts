// Shared Company type definitions to ensure consistency across the application

export interface Company {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  domain: string | null;
  status: 'active' | 'suspended' | 'inactive';
  subscription_plan: string;
  max_users: number;
  settings: any;
  api_tokens: any;
  twilio_account_sid: string | null;
  twilio_auth_token: string | null;
  twilio_phone_number: string | null;
  green_api_instance_id: string | null;
  green_api_token: string | null;
  created_at: string;
  updated_at: string;
  onboarded_at?: string;
  onboarded_by?: string;
  user_count?: number;
}

// For creating new companies (excludes auto-generated fields)
export interface CreateCompanyData {
  name: string;
  email: string;
  phone: string;
  address: string;
  domain?: string | null;
  status?: 'active' | 'suspended' | 'inactive';
  subscription_plan?: string;
  max_users?: number;
  settings?: any;
  api_tokens?: any;
  twilio_account_sid?: string | null;
  twilio_auth_token?: string | null;
  twilio_phone_number?: string | null;
  green_api_instance_id?: string | null;
  green_api_token?: string | null;
  onboarded_at?: string;
  onboarded_by?: string;
}

// For updating companies (all fields optional except id)
export interface UpdateCompanyData {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  domain?: string | null;
  status?: 'active' | 'suspended' | 'inactive';
  subscription_plan?: string;
  max_users?: number;
  settings?: any;
  api_tokens?: any;
  twilio_account_sid?: string | null;
  twilio_auth_token?: string | null;
  twilio_phone_number?: string | null;
  green_api_instance_id?: string | null;
  green_api_token?: string | null;
}



// Company onboarding data structure
export interface CompanyOnboardingData {
  // Company Information
  companyName: string;
  companyEmail: string;
  companyPhone: string;
  companyAddress: string;
  subscriptionPlan: 'basic' | 'pro' | 'enterprise';
  maxUsers: number;
  
  // Admin User Information
  adminFirstName: string;
  adminLastName: string;
  adminEmail: string;
  adminPhone: string;
  
  // Additional Settings
  timezone: string;
  language: string;
  notes?: string;
}
