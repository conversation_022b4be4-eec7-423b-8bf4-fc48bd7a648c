import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Building2,
  Settings,
  MessageSquare,
  Phone,
  Save,
  Eye,
  EyeOff,
  Copy,
  CheckCircle,
  AlertCircle,
  Users
} from 'lucide-react';
import { UserManagement } from '@/components/settings/UserManagement';
import { useCompany } from '@/contexts/CompanyContext';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const companyInfoSchema = z.object({
  name: z.string().min(2, 'Company name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 characters'),
  address: z.string().min(5, 'Address must be at least 5 characters'),
});

const apiTokensSchema = z.object({
  green_api_instance_id: z.string().optional(),
  green_api_token: z.string().optional(),
  whatsapp_number: z.string().optional(),
  twilio_account_sid: z.string().optional(),
  twilio_auth_token: z.string().optional(),
  twilio_phone_number: z.string().optional(),
  twilio_api_key: z.string().optional(),
  twilio_api_secret: z.string().optional(),
  twilio_twiml_app_sid: z.string().optional(),
});

type CompanyInfoData = z.infer<typeof companyInfoSchema>;
type ApiTokensData = z.infer<typeof apiTokensSchema>;

const CompanySettingsPage = () => {
  const { currentCompany, refreshUserCompany, refreshCompanies } = useCompany();
  const { isCompanyAdmin, isSuperAdmin } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [showTokens, setShowTokens] = useState({
    green_api_token: false,
    twilio_auth_token: false,
    twilio_api_secret: false,
  });

  // Company Info Form
  const companyForm = useForm<CompanyInfoData>({
    resolver: zodResolver(companyInfoSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      address: '',
    }
  });

  // API Tokens Form
  const tokensForm = useForm<ApiTokensData>({
    resolver: zodResolver(apiTokensSchema),
    defaultValues: {
      green_api_instance_id: '',
      green_api_token: '',
      whatsapp_number: '',
      twilio_account_sid: '',
      twilio_auth_token: '',
      twilio_phone_number: '',
      twilio_api_key: '',
      twilio_api_secret: '',
      twilio_twiml_app_sid: '',
    }
  });

  // Load company data when component mounts or company changes
  useEffect(() => {
    if (currentCompany) {
      // Set company info form values
      companyForm.reset({
        name: currentCompany.name || '',
        email: currentCompany.email || '',
        phone: currentCompany.phone || '',
        address: currentCompany.address || '',
      });

      // Set API tokens form values - read from direct columns first, fallback to api_tokens JSON
      const apiTokens = currentCompany.api_tokens || {};
      tokensForm.reset({
        green_api_instance_id: currentCompany.green_api_instance_id || apiTokens.green_api_instance_id || '',
        green_api_token: currentCompany.green_api_token || apiTokens.green_api_token || '',
        whatsapp_number: apiTokens.whatsapp_number || '',
        twilio_account_sid: currentCompany.twilio_account_sid || apiTokens.twilio_account_sid || '',
        twilio_auth_token: currentCompany.twilio_auth_token || apiTokens.twilio_auth_token || '',
        twilio_phone_number: currentCompany.twilio_phone_number || apiTokens.twilio_phone_number || '',
        twilio_api_key: currentCompany.twilio_api_key || apiTokens.twilio_api_key || '',
        twilio_api_secret: currentCompany.twilio_api_secret || apiTokens.twilio_api_secret || '',
        twilio_twiml_app_sid: currentCompany.twilio_twiml_app_sid || apiTokens.twilio_twiml_app_sid || '',
      });
    }
  }, [currentCompany, companyForm, tokensForm]);

  // Check permissions
  if (!isCompanyAdmin && !isSuperAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-96">
          <CardHeader>
            <CardTitle className="text-center text-red-600">Access Denied</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center text-muted-foreground">
              You don't have permission to access company settings.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-96">
          <CardHeader>
            <CardTitle className="text-center">No Company Selected</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center text-muted-foreground">
              Please select a company to manage settings.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const updateCompanyInfo = async (data: CompanyInfoData) => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('companies')
        .update({
          name: data.name,
          email: data.email,
          phone: data.phone,
          address: data.address,
          updated_at: new Date().toISOString(),
        })
        .eq('id', currentCompany.id);

      if (error) throw error;

      toast.success('Company information updated successfully');
      
      // Refresh company data
      if (isSuperAdmin) {
        await refreshCompanies();
      } else {
        await refreshUserCompany();
      }
    } catch (error) {
      console.error('Error updating company info:', error);
      toast.error('Failed to update company information');
    } finally {
      setIsLoading(false);
    }
  };

  const updateApiTokens = async (data: ApiTokensData) => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('companies')
        .update({
          // Save to both api_tokens JSON field and direct columns for compatibility
          api_tokens: {
            green_api_instance_id: data.green_api_instance_id,
            green_api_token: data.green_api_token,
            whatsapp_number: data.whatsapp_number,
            twilio_account_sid: data.twilio_account_sid,
            twilio_auth_token: data.twilio_auth_token,
            twilio_phone_number: data.twilio_phone_number,
            twilio_api_key: data.twilio_api_key,
            twilio_api_secret: data.twilio_api_secret,
            twilio_twiml_app_sid: data.twilio_twiml_app_sid,
          },
          // Also save Twilio credentials to direct columns for Supabase functions
          green_api_instance_id: data.green_api_instance_id,
          green_api_token: data.green_api_token,
          twilio_account_sid: data.twilio_account_sid,
          twilio_auth_token: data.twilio_auth_token,
          twilio_phone_number: data.twilio_phone_number,
          twilio_api_key: data.twilio_api_key,
          twilio_api_secret: data.twilio_api_secret,
          twilio_twiml_app_sid: data.twilio_twiml_app_sid,
          updated_at: new Date().toISOString(),
        })
        .eq('id', currentCompany.id);

      if (error) throw error;

      toast.success('API tokens updated successfully');

      // Refresh company data
      if (isSuperAdmin) {
        await refreshCompanies();
      } else {
        await refreshUserCompany();
      }
    } catch (error) {
      console.error('Error updating API tokens:', error);
      toast.error('Failed to update API tokens');
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    try {
      // Check if clipboard API is available
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
          toast.success(`${label} copied to clipboard`);
        }).catch(() => {
          fallbackCopy(text, label);
        });
      } else {
        fallbackCopy(text, label);
      }
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      fallbackCopy(text, label);
    }
  };

  const fallbackCopy = (text: string, label: string) => {
    try {
      // Create a temporary textarea element
      const textarea = document.createElement('textarea');
      textarea.value = text;
      textarea.style.position = 'fixed';
      textarea.style.left = '-999999px';
      textarea.style.top = '-999999px';
      document.body.appendChild(textarea);
      textarea.focus();
      textarea.select();

      const successful = document.execCommand('copy');
      document.body.removeChild(textarea);

      if (successful) {
        toast.success(`${label} copied to clipboard`);
      } else {
        toast.error('Failed to copy to clipboard');
      }
    } catch (error) {
      console.error('Fallback copy failed:', error);
      toast.error('Copy not supported in this browser');
    }
  };

  const getWebhookUrl = () => {
    const instanceId = tokensForm.watch('green_api_instance_id');
    if (!instanceId) return '';
    return `https://jihaizhvpddinhdysscd.supabase.co/functions/v1/green-api-webhook?instanceId=${instanceId}`;
  };

  const toggleTokenVisibility = (field: 'green_api_token' | 'twilio_auth_token' | 'twilio_api_secret') => {
    setShowTokens(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const getConnectionStatus = (service: 'whatsapp' | 'twilio') => {
    const tokens = currentCompany.api_tokens || {};
    
    if (service === 'whatsapp') {
      const hasTokens = tokens.green_api_instance_id && tokens.green_api_token;
      return hasTokens ? 'connected' : 'disconnected';
    } else {
      const hasBasicTokens = tokens.twilio_account_sid && tokens.twilio_auth_token && tokens.twilio_phone_number;
      const hasVoiceTokens = tokens.twilio_api_key && tokens.twilio_api_secret && tokens.twilio_twiml_app_sid;
      return (hasBasicTokens && hasVoiceTokens) ? 'connected' : 'disconnected';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold flex items-center gap-3" dir="rtl">
          <Settings className="h-8 w-8" />
          הגדרות חברה
        </h1>
        <p className="text-muted-foreground mt-2" dir="rtl">
          ניהול פרטי החברה, משתמשים ואינטגרציות API
        </p>
      </div>

      <Tabs defaultValue="company-info" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="company-info" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            פרטי חברה
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            משתמשים
          </TabsTrigger>
          <TabsTrigger value="whatsapp" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            וואטסאפ
          </TabsTrigger>
          <TabsTrigger value="twilio" className="flex items-center gap-2">
            <Phone className="h-4 w-4" />
            טוויליו
          </TabsTrigger>
        </TabsList>

        <TabsContent value="company-info" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle dir="rtl">פרטי החברה</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={companyForm.handleSubmit(updateCompanyInfo)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Company Name *</Label>
                    <Input
                      id="name"
                      {...companyForm.register('name')}
                      placeholder="Legal Firm Ltd."
                    />
                    {companyForm.formState.errors.name && (
                      <p className="text-sm text-red-600">{companyForm.formState.errors.name.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      {...companyForm.register('email')}
                      placeholder="<EMAIL>"
                    />
                    {companyForm.formState.errors.email && (
                      <p className="text-sm text-red-600">{companyForm.formState.errors.email.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label htmlFor="phone">Phone *</Label>
                    <Input
                      id="phone"
                      {...companyForm.register('phone')}
                      placeholder="+972-50-123-4567"
                    />
                    {companyForm.formState.errors.phone && (
                      <p className="text-sm text-red-600">{companyForm.formState.errors.phone.message}</p>
                    )}
                  </div>
                  
                  <div className="md:col-span-2">
                    <Label htmlFor="address">Address *</Label>
                    <Textarea
                      id="address"
                      {...companyForm.register('address')}
                      placeholder="123 Legal Street, Tel Aviv, Israel"
                      rows={3}
                    />
                    {companyForm.formState.errors.address && (
                      <p className="text-sm text-red-600">{companyForm.formState.errors.address.message}</p>
                    )}
                  </div>
                </div>
                
                <Button type="submit" disabled={isLoading} className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  {isLoading ? 'Saving...' : 'Save Changes'}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          <UserManagement />
        </TabsContent>

        <TabsContent value="whatsapp" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  WhatsApp Integration (GreenAPI)
                </CardTitle>
                <Badge
                  variant={getConnectionStatus('whatsapp') === 'connected' ? 'default' : 'secondary'}
                  className="flex items-center gap-1"
                >
                  {getConnectionStatus('whatsapp') === 'connected' ? (
                    <CheckCircle className="h-3 w-3" />
                  ) : (
                    <AlertCircle className="h-3 w-3" />
                  )}
                  {getConnectionStatus('whatsapp') === 'connected' ? 'Connected' : 'Not Connected'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <form onSubmit={tokensForm.handleSubmit(updateApiTokens)} className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="green_api_instance_id">GreenAPI Instance ID</Label>
                    <Input
                      id="green_api_instance_id"
                      {...tokensForm.register('green_api_instance_id')}
                      placeholder="1101000001"
                    />
                  </div>

                  <div>
                    <Label htmlFor="green_api_token">GreenAPI Token</Label>
                    <div className="relative">
                      <Input
                        id="green_api_token"
                        type={showTokens.green_api_token ? 'text' : 'password'}
                        {...tokensForm.register('green_api_token')}
                        placeholder="Enter GreenAPI token"
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => toggleTokenVisibility('green_api_token')}
                      >
                        {showTokens.green_api_token ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="whatsapp_number">WhatsApp Number</Label>
                    <Input
                      id="whatsapp_number"
                      {...tokensForm.register('whatsapp_number')}
                      placeholder="972501234567"
                    />
                  </div>

                  {tokensForm.watch('green_api_instance_id') && (
                    <div className="space-y-2">
                      <Label>Webhook URL</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          value={getWebhookUrl()}
                          readOnly
                          className="font-mono text-sm"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(getWebhookUrl(), 'Webhook URL')}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Configure this URL in your GreenAPI webhook settings
                      </p>
                    </div>
                  )}
                </div>

                <Button type="submit" disabled={isLoading} className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  {isLoading ? 'Saving...' : 'Save WhatsApp Settings'}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="twilio" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Phone className="h-5 w-5" />
                  Twilio Integration
                </CardTitle>
                <Badge
                  variant={getConnectionStatus('twilio') === 'connected' ? 'default' : 'secondary'}
                  className="flex items-center gap-1"
                >
                  {getConnectionStatus('twilio') === 'connected' ? (
                    <CheckCircle className="h-3 w-3" />
                  ) : (
                    <AlertCircle className="h-3 w-3" />
                  )}
                  {getConnectionStatus('twilio') === 'connected' ? 'Connected' : 'Not Connected'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <form onSubmit={tokensForm.handleSubmit(updateApiTokens)} className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="twilio_account_sid">Twilio Account SID</Label>
                    <Input
                      id="twilio_account_sid"
                      {...tokensForm.register('twilio_account_sid')}
                      placeholder="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                    />
                  </div>

                  <div>
                    <Label htmlFor="twilio_auth_token">Twilio Auth Token</Label>
                    <div className="relative">
                      <Input
                        id="twilio_auth_token"
                        type={showTokens.twilio_auth_token ? 'text' : 'password'}
                        {...tokensForm.register('twilio_auth_token')}
                        placeholder="Enter Twilio auth token"
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => toggleTokenVisibility('twilio_auth_token')}
                      >
                        {showTokens.twilio_auth_token ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="twilio_phone_number">Twilio Phone Number</Label>
                    <Input
                      id="twilio_phone_number"
                      {...tokensForm.register('twilio_phone_number')}
                      placeholder="+1234567890"
                    />
                  </div>

                  <div className="border-t pt-4">
                    <h4 className="text-sm font-medium mb-3">Voice SDK Credentials (Required for Voice Calls)</h4>

                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="twilio_api_key">Twilio API Key</Label>
                        <Input
                          id="twilio_api_key"
                          {...tokensForm.register('twilio_api_key')}
                          placeholder="SKxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                        />
                      </div>

                      <div>
                        <Label htmlFor="twilio_api_secret">Twilio API Secret</Label>
                        <div className="relative">
                          <Input
                            id="twilio_api_secret"
                            type={showTokens.twilio_api_secret ? 'text' : 'password'}
                            {...tokensForm.register('twilio_api_secret')}
                            placeholder="Enter Twilio API secret"
                            className="pr-10"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3"
                            onClick={() => toggleTokenVisibility('twilio_api_secret')}
                          >
                            {showTokens.twilio_api_secret ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="twilio_twiml_app_sid">TwiML App SID</Label>
                        <Input
                          id="twilio_twiml_app_sid"
                          {...tokensForm.register('twilio_twiml_app_sid')}
                          placeholder="APxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    These credentials will be used for making phone calls through Twilio.
                    The Voice SDK credentials (API Key, API Secret, TwiML App SID) are required for voice calls to work.
                    Make sure your Twilio account has sufficient credits and the phone number is verified.
                  </AlertDescription>
                </Alert>

                <Alert className="bg-blue-50 border-blue-200">
                  <AlertCircle className="h-4 w-4 text-blue-600" />
                  <AlertDescription className="text-blue-800">
                    <strong>How to get Voice SDK credentials:</strong><br />
                    1. Go to your Twilio Console → Account → API Keys & Tokens<br />
                    2. Create a new API Key (copy the Key and Secret)<br />
                    3. Go to Voice → TwiML → TwiML Apps<br />
                    4. Create a new TwiML App with Voice webhook URL:<br />
                    <div className="mt-2 flex items-center gap-2">
                      <Input
                        value="https://jihaizhvpddinhdysscd.supabase.co/functions/v1/voice-twiml"
                        readOnly
                        className="font-mono text-sm bg-blue-100"
                        onClick={(e) => e.currentTarget.select()}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard('https://jihaizhvpddinhdysscd.supabase.co/functions/v1/voice-twiml', 'Voice webhook URL')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                    5. Copy the TwiML App SID
                  </AlertDescription>
                </Alert>

                <Button type="submit" disabled={isLoading} className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  {isLoading ? 'Saving...' : 'Save Twilio Settings'}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CompanySettingsPage;
