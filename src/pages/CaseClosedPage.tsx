import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Eye, Calendar, User, FileText, Loader2 } from "lucide-react";
import { useCases } from "@/hooks/useCases";

const CaseClosedPage = () => {
  const navigate = useNavigate();
  const { cases, isLoading } = useCases();

  const closedCases = cases.filter(case_ => case_.status === "סגור");

  const CasesTable = ({ cases }: { cases: typeof closedCases }) => (
    <div className="card-professional rounded-lg overflow-hidden">
      <table className="professional-table">
        <thead>
          <tr>
            <th>פעולות</th>
            <th>דדליין</th>
            <th>סוג התיק</th>
            <th>לקוח</th>
            <th>שם התיק</th>
          </tr>
        </thead>
        <tbody>
          {cases.map((case_) => (
            <tr
              key={case_.id}
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => navigate(`/case/${case_.id}`)}
            >
              <td>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(`/case/${case_.id}`);
                  }}
                >
                  <Eye className="w-4 h-4" />
                </Button>
              </td>
              <td className="text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  {case_.deadline ? new Date(case_.deadline).toLocaleDateString('he-IL') : "לא צוין"}
                </div>
              </td>
              <td>
                <span className="text-sm font-medium">{case_.case_type?.name || "לא צוין"}</span>
              </td>
              <td>
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4 text-muted-foreground" />
                  {case_.lead?.full_name || "לא צוין"}
                </div>
              </td>
              <td className="font-medium">
                <div className="flex items-center gap-2">
                  <FileText className="w-4 h-4 text-muted-foreground" />
                  {case_.title}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground mb-2">תיקים סגורים</h1>
          <p className="text-muted-foreground">תיקים שהושלמו בהצלחה</p>
        </div>
        <Button 
          className="btn-professional flex items-center gap-2"
          onClick={() => navigate('/cases/intake')}
        >
          <Plus className="w-4 h-4" />
          צור תיק חדש
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
            תיקים סגורים ({closedCases.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin" />
            </div>
          ) : closedCases.length > 0 ? (
            <CasesTable cases={closedCases} />
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="w-12 h-12 mx-auto mb-4" />
              <p>אין תיקים סגורים</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CaseClosedPage;