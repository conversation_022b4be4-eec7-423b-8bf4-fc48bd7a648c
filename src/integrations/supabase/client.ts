// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://jihaizhvpddinhdysscd.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppaGFpemh2cGRkaW5oZHlzc2NkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTU4ODQwNjIsImV4cCI6MjA3MTQ2MDA2Mn0.EmbivKVP4hlM3WI5WpYOLrNvmmTACm9AnwrWRyMqc6I";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});