@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional Legal Practice Management Design System */
/* All colors MUST be HSL for proper theming */

@layer base {
  :root {
    /* Professional Legal Theme - Light Mode - Clean White Background */
    --background: 255 255 255;  /* Pure white background */
    --foreground: 15 23 42;     /* slate-800 - dark text */

    --card: 255 255 255;        /* Pure white cards */
    --card-foreground: 15 23 42; /* slate-800 - dark text */

    --popover: 255 255 255;     /* Pure white popover */
    --popover-foreground: 15 23 42; /* dark text */

    /* Primary Color - Your Accent Color #003E66 */
    --primary: 210 100% 20%;    /* #003E66 - Your specified accent color */
    --primary-foreground: 255 255 255;
    --primary-light: 210 100% 30%;  /* Lighter version */
    --primary-dark: 210 100% 15%;   /* Darker version */

    /* Secondary - Neutral for secondary elements */
    --secondary: 210 16% 93%;   /* Very light gray */
    --secondary-foreground: 15 23 42; /* dark text on light background */

    /* Neutral Grays - Clean and minimal */
    --muted: 210 16% 98%;       /* Very light gray background */
    --muted-foreground: 15 23 42; /* dark text */

    /* Accent - Teal from your logo */
    --accent: 180 100% 90%;     /* Light teal background */
    --accent-foreground: 180 100% 25%; /* Dark teal text */

    /* Professional Status Colors */
    --success: 142 76% 36%;     /* emerald-600 */
    --success-foreground: 255 255 255;

    --warning: 25 95% 53%;      /* orange-400 */
    --warning-foreground: 255 255 255;

    --destructive: 0 72% 51%;   /* red-500 */
    --destructive-foreground: 255 255 255;

    /* Borders & Inputs - Light and clean */
    --border: 210 16% 90%;      /* Light gray border */
    --input: 210 16% 95%;       /* Very light gray input background */
    --ring: 210 100% 20%;       /* matches primary */

    --radius: 0.5rem;

    /* Sidebar Professional Theme - All Text Dark */
    --sidebar-background: 248 250 252; /* slate-50 */
    --sidebar-foreground: 15 23 42;    /* dark text */
    --sidebar-primary: 220 38% 48%;    /* Professional blue */
    --sidebar-primary-foreground: 255 255 255;
    --sidebar-accent: 241 245 249;     /* slate-100 */
    --sidebar-accent-foreground: 15 23 42; /* dark text */
    --sidebar-border: 226 232 240;     /* slate-200 */
    --sidebar-ring: 220 38% 48%;

    /* Professional Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-light)) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary-dark)) 0%, hsl(var(--primary)) 50%, hsl(var(--primary-light)) 100%);
    --gradient-subtle: linear-gradient(180deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);

    /* Professional Shadows */
    --shadow-sm: 0 1px 2px 0 hsl(var(--primary) / 0.05);
    --shadow-md: 0 4px 6px -1px hsl(var(--primary) / 0.1), 0 2px 4px -2px hsl(var(--primary) / 0.1);
    --shadow-lg: 0 10px 15px -3px hsl(var(--primary) / 0.1), 0 4px 6px -4px hsl(var(--primary) / 0.1);
    --shadow-xl: 0 20px 25px -5px hsl(var(--primary) / 0.1), 0 8px 10px -6px hsl(var(--primary) / 0.1);

    /* Professional Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .dark {
    /* Dark Mode Professional Theme - True Dark Colors */
    --background: 0 0% 7%;       /* Very dark gray #121212 */
    --foreground: 0 0% 95%;      /* Almost white text */

    --card: 0 0% 10%;           /* Dark card background #1a1a1a */
    --card-foreground: 0 0% 95%; /* Light text on cards */

    --popover: 0 0% 8%;         /* Dark popover */
    --popover-foreground: 0 0% 95%;

    /* Primary Color - Your Accent Color #003E66 (brighter for dark mode) */
    --primary: 210 100% 50%;    /* Brighter blue for dark mode visibility */
    --primary-foreground: 0 0% 100%; /* Pure white text */

    --secondary: 0 0% 15%;      /* Dark secondary background */
    --secondary-foreground: 0 0% 90%; /* Light text on secondary */

    --muted: 0 0% 12%;          /* Muted dark background */
    --muted-foreground: 0 0% 70%; /* Medium gray text */

    /* Accent - Teal from your logo (bright for dark mode) */
    --accent: 180 100% 40%;     /* Bright teal for visibility */
    --accent-foreground: 0 0% 100%;

    --warning: 25 95% 60%;      /* Brighter orange for dark mode */
    --warning-foreground: 0 0% 100%;

    --destructive: 0 70% 60%;   /* Brighter red for dark mode */
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 20%;         /* Dark border */
    --input: 0 0% 12%;          /* Dark input background */
    --ring: 210 100% 50%;       /* matches primary */

    --sidebar-background: 0 0% 8%;
    --sidebar-foreground: 0 0% 90%;
    --sidebar-primary: 210 100% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 15%;
    --sidebar-accent-foreground: 0 0% 90%;
    --sidebar-border: 0 0% 20%;
    --sidebar-ring: 210 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    direction: rtl;
  }

  body {
    @apply bg-background text-foreground font-assistant text-base;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-size: 16px; /* Slightly larger base font size */
  }

  /* Global container styling */
  #root {
    min-height: 100vh;
  }

  /* Hebrew Typography Optimizations */
  .hebrew-text {
    font-family: 'Assistant', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    font-feature-settings: "kern" 1;
    text-rendering: optimizeLegibility;
  }

  /* Slightly larger font sizes for better readability */
  p, span, div {
    font-size: 1rem; /* 16px */
  }

  .text-sm {
    font-size: 0.9rem !important; /* 14.4px instead of 14px */
  }

  .text-xs {
    font-size: 0.8rem !important; /* 12.8px instead of 12px */
  }

  /* Table text improvements */
  table {
    font-size: 1rem;
  }

  th, td {
    font-size: 0.95rem; /* Slightly larger than default */
    vertical-align: middle; /* Center align vertically */
  }

  /* Professional table styling */
  .professional-table th {
    text-align: center;
    font-weight: 600;
    padding: 12px 8px;
  }

  .professional-table td {
    text-align: center;
    padding: 12px 8px;
  }

  /* Professional Button Styles - Gradient Blue */
  .btn-professional {
    @apply bg-gradient-to-r from-primary to-primary-light text-primary-foreground;
    @apply shadow-md hover:shadow-lg transition-all duration-300;
    @apply border border-primary/20;
  }

  /* All Buttons Use Blue Gradient */
  .btn-primary {
    @apply bg-gradient-to-r from-primary to-primary-light text-primary-foreground;
    @apply hover:from-primary-dark hover:to-primary transition-all duration-300;
  }

  /* All Icons Use Primary Blue */
  .icon-primary {
    @apply text-primary;
  }

  /* Professional Card Styles */
  .card-professional {
    @apply bg-card border border-border shadow-sm;
    @apply hover:shadow-md transition-shadow duration-300;
  }

  /* Status Indicators */
  .status-success {
    @apply bg-success/10 text-success border border-success/20;
  }

  .status-warning {
    @apply bg-warning/10 text-warning-foreground border border-warning/30;
  }

  .status-pending {
    @apply bg-muted text-muted-foreground border border-border;
  }

  /* Dark mode specific improvements */
  .dark .shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  }

  .dark .shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  }

  .dark .shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
  }
}

/* RTL Specific Overrides */
.rtl-flip {
  transform: scaleX(-1);
}

/* Professional Data Table Styles */
.professional-table {
  @apply w-full border-collapse;
}

.professional-table th {
  @apply bg-muted/30 text-foreground font-semibold px-4 py-3 text-right border-b-2 border-primary/20;
}

.professional-table td {
  @apply px-4 py-3 text-right border-b border-border/50 text-foreground;
}

.professional-table tr:hover {
  @apply bg-primary/5;
}

/* Professional Form Styles */
.form-professional label {
  @apply text-sm font-semibold text-foreground mb-2 block;
}

.form-professional input,
.form-professional select,
.form-professional textarea {
  @apply w-full px-3 py-2 border border-input bg-background text-foreground font-medium;
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary;
  @apply transition-colors duration-200;
}

/* WhatsApp Container Glow Effects */
.whatsapp-glow {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px hsl(var(--accent) / 0.2),
    0 0 20px hsl(var(--accent) / 0.15);
  border: 1px solid hsl(var(--accent) / 0.3);
  transition: all 0.3s ease;
}

.whatsapp-glow:hover {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 0 0 1px hsl(var(--accent) / 0.3),
    0 0 30px hsl(var(--accent) / 0.25);
}