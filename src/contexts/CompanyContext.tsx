import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';

interface Company {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  status: 'active' | 'suspended' | 'inactive';
  subscription_plan: string;
  max_users: number;
  settings: any;
  api_tokens: any;
  twilio_account_sid: string | null;
  twilio_auth_token: string | null;
  twilio_phone_number: string | null;
  green_api_instance_id: string | null;
  green_api_token: string | null;
  created_at: string;
  updated_at: string;
  onboarded_at?: string;
  onboarded_by?: string;
  user_count?: number;
}

interface CompanyContextType {
  // Current selected company (for super admins)
  selectedCompany: Company | null;
  setSelectedCompany: (company: Company | null) => void;

  // User's own company (for regular users)
  userCompany: Company | null;

  // All companies (for super admins)
  allCompanies: Company[];

  // Current active company (either selected or user's company)
  currentCompany: Company | null;

  // Loading states
  loading: boolean;
  companiesLoading: boolean;

  // Actions
  refreshCompanies: () => Promise<void>;
  refreshUserCompany: () => Promise<void>;
  handleCompanyCreated: (newCompany: Company) => Promise<void>;
  handleCompanyDeleted: (deletedCompanyId: string) => Promise<void>;
  handleCompanyUpdated: (updatedCompany: Company) => Promise<void>;
  switchCompany: (company: Company | null) => void;
}

const CompanyContext = createContext<CompanyContextType | undefined>(undefined);

export const useCompany = () => {
  const context = useContext(CompanyContext);
  if (context === undefined) {
    throw new Error('useCompany must be used within a CompanyProvider');
  }
  return context;
};

interface CompanyProviderProps {
  children: ReactNode;
}

export const CompanyProvider: React.FC<CompanyProviderProps> = ({ children }) => {
  const { user, userRole, isSuperAdmin, loading: authLoading } = useAuth();
  const queryClient = useQueryClient();

  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [userCompany, setUserCompany] = useState<Company | null>(null);
  const [allCompanies, setAllCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(true);
  const [companiesLoading, setCompaniesLoading] = useState(false);

  // Fetch user's company (for regular users)
  const fetchUserCompany = async () => {
    if (!user || !userRole?.company_id || isSuperAdmin) {
      setUserCompany(null);
      return;
    }

    try {
      const { data: company, error } = await supabase
        .from('companies')
        .select('*')
        .eq('id', userRole.company_id)
        .single();

      if (error) {
        console.error('Error fetching user company:', error);
        toast.error('Failed to load company information');
        setUserCompany(null);
      } else {
        setUserCompany(company);
      }
    } catch (err) {
      console.error('Exception fetching user company:', err);
      setUserCompany(null);
    }
  };

  // Fetch all companies (for super admins)
  const fetchAllCompanies = async () => {
    if (!isSuperAdmin) {
      setAllCompanies([]);
      return;
    }

    setCompaniesLoading(true);
    try {
      const { data: companies, error } = await supabase
        .from('companies')
        .select(`
          *,
          user_count:user_roles(count)
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching companies:', error);
        toast.error('Failed to load companies');
        setAllCompanies([]);
      } else {
        // Process the user_count from the aggregated query
        const processedCompanies = companies.map(company => ({
          ...company,
          user_count: Array.isArray(company.user_count) ? company.user_count.length : 0
        }));
        setAllCompanies(processedCompanies);

        // Check if currently selected company still exists
        if (selectedCompany) {
          const stillExists = processedCompanies.find(c => c.id === selectedCompany.id);
          if (!stillExists) {
            setSelectedCompany(null);
            // Clear all caches when company is deleted
            queryClient.clear();
          }
        }

        // If no company is selected and there are companies, select the first one
        if (!selectedCompany && processedCompanies.length > 0) {
          setSelectedCompany(processedCompanies[0]);
        }
      }
    } catch (err) {
      console.error('Exception fetching companies:', err);
      setAllCompanies([]);
    } finally {
      setCompaniesLoading(false);
    }
  };

  // Refresh functions
  const refreshCompanies = async () => {
    await fetchAllCompanies();
  };

  const refreshUserCompany = async () => {
    await fetchUserCompany();
  };

  // Handle company creation with optimistic update
  const handleCompanyCreated = async (newCompany: Company) => {
    // Optimistically add the company to the list
    setAllCompanies(prev => [newCompany, ...prev]);

    // If no company is selected, select the new one
    if (!selectedCompany) {
      setSelectedCompany(newCompany);
    }

    // Refresh to ensure data consistency
    await refreshCompanies();
  };

  // Handle company deletion with optimistic update
  const handleCompanyDeleted = async (deletedCompanyId: string) => {
    // Optimistically remove the company from the list
    setAllCompanies(prev => prev.filter(c => c.id !== deletedCompanyId));

    // If the deleted company was selected, clear selection
    if (selectedCompany?.id === deletedCompanyId) {
      setSelectedCompany(null);
    }

    // Clear all caches to prevent stale data
    queryClient.clear();

    // Refresh companies list to ensure consistency
    await refreshCompanies();
  };

  // Handle company update with optimistic update
  const handleCompanyUpdated = async (updatedCompany: Company) => {
    // Optimistically update the company in the list
    setAllCompanies(prev =>
      prev.map(c => c.id === updatedCompany.id ? updatedCompany : c)
    );

    // Update selected company if it's the one being updated
    if (selectedCompany?.id === updatedCompany.id) {
      setSelectedCompany(updatedCompany);
    }

    // Refresh to ensure data consistency
    await refreshCompanies();
  };

  // Initialize data when auth is ready
  useEffect(() => {
    if (authLoading) return;

    const initializeCompanyData = async () => {
      setLoading(true);
      
      if (isSuperAdmin) {
        await fetchAllCompanies();
      } else {
        await fetchUserCompany();
      }
      
      setLoading(false);
    };

    initializeCompanyData();
  }, [user, userRole, isSuperAdmin, authLoading]);

  // Reset selected company when user role changes
  useEffect(() => {
    if (!isSuperAdmin) {
      setSelectedCompany(null);
    }
  }, [isSuperAdmin]);

  // Determine current active company
  const currentCompany = isSuperAdmin ? selectedCompany : userCompany;



  // Optimized company switching function
  const switchCompany = (company: Company | null) => {
    setSelectedCompany(company);

    // Optimized cache invalidation - only invalidate what's necessary
    if (selectedCompany?.id && company?.id !== selectedCompany.id) {
      // Only invalidate queries for the previous company
      queryClient.invalidateQueries({
        predicate: (query) => {
          return query.queryKey.includes(selectedCompany.id);
        }
      });
    }
  };

  const value: CompanyContextType = {
    selectedCompany,
    setSelectedCompany: switchCompany, // Use our custom function
    userCompany,
    allCompanies,
    currentCompany,
    loading,
    companiesLoading,
    refreshCompanies,
    refreshUserCompany,
    handleCompanyCreated,
    handleCompanyDeleted,
    handleCompanyUpdated,
    switchCompany
  };

  return (
    <CompanyContext.Provider value={value}>
      {children}
    </CompanyContext.Provider>
  );
};
