import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

interface UserRole {
  id: string;
  role: 'super_admin' | 'company_admin' | 'user';
  company_id: string | null;
}

interface AuthContextType {
  user: User | null;
  session: Session | null;
  userRole: UserRole | null;
  loading: boolean;
  signOut: () => Promise<void>;
  isSuperAdmin: boolean;
  isCompanyAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchUserRole = async (userId: string) => {
    try {
      console.log('AuthProvider: Fetching user role for', userId);
      const { data: role, error } = await supabase
        .from('user_roles')
        .select('id, role, company_id')
        .eq('user_id', userId)
        .single();
      
      if (error) {
        console.warn('AuthProvider: Error fetching user role:', error);
        setUserRole(null);
      } else {
        console.log('AuthProvider: User role fetched:', role);
        setUserRole(role);
      }
    } catch (err) {
      console.error('AuthProvider: Exception fetching user role:', err);
      setUserRole(null);
    }
  };

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('AuthProvider: Setting up auth state listener');
    }

    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (process.env.NODE_ENV === 'development') {
          console.log('AuthProvider: Auth state changed', { event, hasSession: !!session });
        }

        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          // Use setTimeout to avoid blocking the auth state change
          setTimeout(() => {
            fetchUserRole(session.user.id);
          }, 0);
        } else {
          setUserRole(null);
        }

        setLoading(false);
      }
    );

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('AuthProvider: Initial session check', { hasSession: !!session });
      }

      setSession(session);
      setUser(session?.user ?? null);

      if (session?.user) {
        fetchUserRole(session.user.id);
      }

      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signOut = async () => {
    await supabase.auth.signOut();
  };

  const isSuperAdmin = userRole?.role === 'super_admin';
  const isCompanyAdmin = userRole?.role === 'company_admin';

  const value = {
    user,
    session,
    userRole,
    loading,
    signOut,
    isSuperAdmin,
    isCompanyAdmin
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};