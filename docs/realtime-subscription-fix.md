# Real-time Subscription Duplicate Fix

## Problem
The application was creating **duplicate real-time subscriptions** to Supabase tables, causing multiple events to be triggered for the same database changes. This was evident in the console logs showing:

```
Answer status change received: {...}
Lead change received: {...}
Answer status change received: {...} // DUPLICATE
```

## Root Cause
Multiple React components were using the `useLeads` hook simultaneously:
- `LeadsPage.tsx`
- `LeadsTab.tsx` (Office page)
- `WhatsAppPage.tsx`
- `ReactivationCampaign.tsx`

Each instance of `useLeads` was creating its own real-time subscriptions to the same tables (`leads` and `lead_answer_status`), resulting in duplicate event handlers.

## Solution
Created a **centralized real-time subscription manager** that prevents duplicate subscriptions:

### 1. New Subscription Manager (`useRealtimeSubscriptions.ts`)
- **Singleton pattern** ensures only one subscription per table/event/company combination
- **Callback aggregation** allows multiple components to listen to the same subscription
- **Automatic cleanup** removes subscriptions when no components are listening
- **Company-scoped filtering** ensures data isolation

### 2. Updated Hooks
- **`useLeads`**: Removed direct subscription code, now uses centralized manager
- **`useWhatsApp`**: Updated to use centralized manager for WhatsApp tables
- **Memoized callbacks** prevent unnecessary re-subscriptions

### 3. Key Features
- **Deduplication**: Only one subscription per table/event combination
- **Resource efficiency**: Automatic cleanup when no listeners remain
- **Debug logging**: Clear visibility into subscription lifecycle
- **Type safety**: Full TypeScript support

## Benefits
1. **Performance**: Eliminates duplicate network requests and event processing
2. **Reliability**: Prevents race conditions from multiple subscriptions
3. **Maintainability**: Centralized subscription logic
4. **Debugging**: Clear logging of subscription lifecycle
5. **Resource efficiency**: Automatic cleanup prevents memory leaks

## Usage Examples

### Basic subscription
```typescript
const { subscribe } = useRealtimeSubscriptions();

useEffect(() => {
  const unsubscribe = subscribe('leads', '*', (payload) => {
    console.log('Lead changed:', payload);
  });
  
  return unsubscribe;
}, []);
```

### Convenience hooks
```typescript
// For leads table
useLeadsRealtimeSubscription(callback);

// For lead answer status
useLeadAnswerStatusRealtimeSubscription(callback);

// For WhatsApp tables
useWhatsAppRealtimeSubscriptions(conversationCallback, messageCallback);
```

## Testing
1. Open browser console
2. Navigate to leads page
3. Update a lead's answer status
4. Verify only **one** event is logged per actual database change
5. Check subscription logs show proper creation/reuse/cleanup

## Migration Notes
- All existing functionality preserved
- No breaking changes to component APIs
- Automatic migration - no manual intervention required
- Improved performance and reliability

## Future Enhancements
- Could extend to other real-time tables (cases, activities, etc.)
- Add subscription health monitoring
- Implement reconnection logic for network issues
- Add subscription analytics/metrics
