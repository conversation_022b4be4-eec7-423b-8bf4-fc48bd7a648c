# Real-time Subscription Optimization Fix

## Problem Identified

You were experiencing **constant subscription creation and removal** in your application logs, showing patterns like:

```
🔗 Creating real-time subscription for lead_answer_status (*) - Total callbacks: 1
🔌 Removing real-time subscription for lead_answer_status (*)
🔗 Creating real-time subscription for lead_answer_status (*) - Total callbacks: 1
```

This was happening **continuously while idle**, causing unnecessary server stress and potential performance issues.

## Root Cause Analysis

The issue was caused by **unstable callback references** in the real-time subscription hooks:

### 1. **Callback Dependency Problem**
```typescript
// BEFORE (Problematic)
export const useLeadsRealtimeSubscription = (callback: SubscriptionCallback) => {
  const { subscribe } = useRealtimeSubscriptions();
  
  useEffect(() => {
    const unsubscribe = subscribe('leads', '*', callback);
    return unsubscribe;
  }, [callback, subscribe]); // ❌ callback changes on every render
};
```

### 2. **Multiple Component Usage**
Multiple components were using `useLeads` simultaneously:
- **LeadsPage.tsx** - Main leads page
- **LeadsTab.tsx** - Office page leads tab  
- **WhatsAppPage.tsx** - WhatsApp page (for lead details)
- **ReactivationCampaign.tsx** - Marketing automation

Each component created its own callback function, causing subscription churn.

### 3. **Unstable Subscribe Function**
The `subscribe` function was being recreated on company changes, triggering re-subscriptions.

## Solution Implemented

### 1. **Stable Callback References**
```typescript
// AFTER (Optimized)
export const useLeadsRealtimeSubscription = (callback: SubscriptionCallback) => {
  const { subscribe } = useRealtimeSubscriptions();
  const callbackRef = useRef(callback);
  
  // Update callback ref without triggering re-subscription
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);
  
  useEffect(() => {
    // Use a stable wrapper function that calls the current callback
    const stableCallback = (payload: any) => {
      callbackRef.current(payload);
    };
    
    const unsubscribe = subscribe('leads', '*', stableCallback);
    return unsubscribe;
  }, [subscribe]); // ✅ Only depend on subscribe, not callback
};
```

### 2. **Stable Subscribe Function**
```typescript
// Create a stable subscribe function using useCallback
const subscribe = useCallback((table: string, event: string, callback: SubscriptionCallback) => {
  if (!currentCompanyRef.current) return () => {};

  const unsubscribe = manager.current.subscribe(currentCompanyRef.current, {
    table,
    event,
    callback
  });

  unsubscribeFunctions.current.push(unsubscribe);
  return unsubscribe;
}, []); // ✅ No dependencies - function is stable
```

### 3. **Debounced Cleanup**
```typescript
// Add delay to prevent rapid subscription recreation
setTimeout(() => {
  const currentCallbacks = this.callbacks.get(key);
  // Only remove if still no callbacks after delay
  if (!currentCallbacks || currentCallbacks.size === 0) {
    console.log(`🗑️ Removing real-time subscription for ${config.table} (${config.event})`);
    const channel = this.subscriptions.get(key);
    if (channel) {
      supabase.removeChannel(channel);
      this.subscriptions.delete(key);
    }
    this.callbacks.delete(key);
  }
}, 100); // 100ms delay
```

### 4. **Memoized fetchLeads Function**
```typescript
const fetchLeads = useCallback(async () => {
  // ... fetch logic
}, [currentCompany]); // ✅ Properly memoized
```

## Expected Results

After these optimizations, you should see:

1. **Reduced Subscription Churn**: Subscriptions should be created once and reused
2. **Better Performance**: Less network overhead and CPU usage
3. **Cleaner Logs**: Fewer subscription creation/removal messages
4. **Server Efficiency**: Reduced stress on your Supabase real-time infrastructure

## Monitoring

The logs now include company IDs for better tracking:
```
🔗 Creating real-time subscription for leads (*) - Total callbacks: 3 [Company: abc-123]
♻️ Reusing existing subscription for leads (*) - Total callbacks: 4 [Company: abc-123]
```

## Performance Impact

- **Before**: Each component render could trigger subscription recreation
- **After**: Subscriptions are stable and only created when truly needed
- **Network**: Significantly reduced real-time connection overhead
- **Memory**: Better cleanup and resource management

This fix should eliminate the constant subscription cycling you were experiencing while idle.
