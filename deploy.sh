#!/bin/bash

# Legal Hebrew Nexus - Deployment Script
# This script automates the deployment process to Vercel

echo "🚀 Legal Hebrew Nexus - Deployment Script"
echo "=========================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "⚠️  Node.js version $NODE_VERSION detected. Recommended: 18+"
fi

echo "✅ Node.js $(node -v) detected"

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
fi

# Run build to verify everything works
echo "🔨 Building application..."
npm run build
if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please fix errors before deploying."
    exit 1
fi

echo "✅ Build successful!"

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "📥 Installing Vercel CLI..."
    npm install -g vercel
fi

echo "🌐 Starting Vercel deployment..."
echo ""
echo "Next steps:"
echo "1. You'll be prompted to login to Vercel"
echo "2. Choose your preferred login method (GitHub recommended)"
echo "3. Follow the setup prompts"
echo "4. Your app will be deployed automatically!"
echo ""

# Start Vercel deployment
vercel

echo ""
echo "🎉 Deployment process initiated!"
echo "📋 Check the output above for your deployment URL"
echo "🔗 Visit your Vercel dashboard to manage the project"
