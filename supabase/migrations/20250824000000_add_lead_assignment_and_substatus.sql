-- Add lead assignment and answer status system
-- This migration adds assigned user to leads and creates answer status tracking

-- Add assigned user to leads table (similar to cases)
ALTER TABLE public.leads 
ADD COLUMN IF NOT EXISTS assigned_user_id UUID REFERENCES auth.users(id);

-- Ensure company_id exists on leads table
ALTER TABLE public.leads 
ADD COLUMN IF NOT EXISTS company_id UUID REFERENCES public.companies(id);

-- Create lead answer status tracking table
CREATE TABLE IF NOT EXISTS public.lead_answer_status (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  lead_id UUID NOT NULL REFERENCES public.leads(id) ON DELETE CASCADE,
  company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
  main_status TEXT NOT NULL DEFAULT 'לא ענה',
  answer_status TEXT NOT NULL, -- "לא ענה 1", "לא ענה 2", etc.
  attempt_number INTEGER NOT NULL DEFAULT 1 CHECK (attempt_number >= 1 AND attempt_number <= 7),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  -- Ensure only one active answer status per lead
  CONSTRAINT unique_lead_answer_status UNIQUE (lead_id)
);

-- Update workflow trigger types to include answer status changes
ALTER TABLE public.workflows 
DROP CONSTRAINT IF EXISTS workflows_trigger_type_check,
ADD CONSTRAINT workflows_trigger_type_check 
CHECK (trigger_type IN ('lead_status_change', 'case_status_change', 'lead_answer_status_change', 'manual_campaign'));

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_leads_assigned_user ON public.leads(assigned_user_id);
CREATE INDEX IF NOT EXISTS idx_leads_company_status ON public.leads(company_id, status);
CREATE INDEX IF NOT EXISTS idx_lead_answer_status_lead ON public.lead_answer_status(lead_id);
CREATE INDEX IF NOT EXISTS idx_lead_answer_status_company ON public.lead_answer_status(company_id);
CREATE INDEX IF NOT EXISTS idx_lead_answer_status_attempt ON public.lead_answer_status(attempt_number);

-- Enable RLS for lead_answer_status
ALTER TABLE public.lead_answer_status ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for lead_answer_status
CREATE POLICY "Users can view answer status for their company leads" 
ON public.lead_answer_status 
FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles ur 
    WHERE ur.user_id = auth.uid() 
    AND ur.company_id = lead_answer_status.company_id
  )
);

CREATE POLICY "Users can create answer status for their company leads" 
ON public.lead_answer_status 
FOR INSERT 
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.user_roles ur 
    WHERE ur.user_id = auth.uid() 
    AND ur.company_id = lead_answer_status.company_id
  )
);

CREATE POLICY "Users can update answer status for their company leads" 
ON public.lead_answer_status 
FOR UPDATE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles ur 
    WHERE ur.user_id = auth.uid() 
    AND ur.company_id = lead_answer_status.company_id
  )
);

-- Update existing leads to have company_id if missing
UPDATE public.leads 
SET company_id = (
  SELECT ur.company_id 
  FROM public.user_roles ur 
  WHERE ur.user_id = leads.user_id 
  LIMIT 1
)
WHERE company_id IS NULL;

-- Create trigger for automatic timestamp updates on lead_answer_status
CREATE TRIGGER update_lead_answer_status_updated_at
    BEFORE UPDATE ON public.lead_answer_status
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Enable realtime for lead_answer_status table
ALTER TABLE public.lead_answer_status REPLICA IDENTITY FULL;
ALTER PUBLICATION supabase_realtime ADD TABLE public.lead_answer_status;

-- Add comments for documentation
COMMENT ON TABLE public.lead_answer_status IS 'Tracks answer status attempts for leads (לא ענה 1-7)';
COMMENT ON COLUMN public.leads.assigned_user_id IS 'User assigned to handle this lead';
COMMENT ON COLUMN public.lead_answer_status.answer_status IS 'Current answer status (לא ענה 1, לא ענה 2, etc.)';
COMMENT ON COLUMN public.lead_answer_status.attempt_number IS 'Number of contact attempts (1-7)';

-- Create function to upsert lead answer status (bypasses RLS issues)
CREATE OR REPLACE FUNCTION public.upsert_lead_answer_status(
  p_lead_id UUID,
  p_user_id UUID,
  p_attempt_number INTEGER,
  p_answer_status TEXT
)
RETURNS public.lead_answer_status
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result_record public.lead_answer_status;
  lead_company_id UUID;
BEGIN
  -- Get the company_id for the lead
  SELECT company_id INTO lead_company_id
  FROM public.leads
  WHERE id = p_lead_id;

  IF lead_company_id IS NULL THEN
    RAISE EXCEPTION 'Lead not found or no company associated';
  END IF;

  -- Verify user has access to this company
  IF NOT EXISTS (
    SELECT 1 FROM public.user_roles ur
    WHERE ur.user_id = p_user_id
    AND ur.company_id = lead_company_id
  ) THEN
    RAISE EXCEPTION 'User does not have access to this company';
  END IF;

  -- Upsert the answer status
  INSERT INTO public.lead_answer_status (
    lead_id,
    company_id,
    answer_status,
    attempt_number,
    created_by,
    created_at,
    updated_at
  )
  VALUES (
    p_lead_id,
    lead_company_id,
    p_answer_status,
    p_attempt_number,
    p_user_id,
    NOW(),
    NOW()
  )
  ON CONFLICT (lead_id)
  DO UPDATE SET
    answer_status = EXCLUDED.answer_status,
    attempt_number = EXCLUDED.attempt_number,
    updated_at = NOW()
  RETURNING * INTO result_record;

  RETURN result_record;
END;
$$;

-- Create function to reset lead answer status (bypasses RLS issues)
CREATE OR REPLACE FUNCTION public.reset_lead_answer_status(
  lead_uuid UUID,
  user_uuid UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  lead_company_id UUID;
BEGIN
  -- Get the company_id for the lead
  SELECT company_id INTO lead_company_id
  FROM public.leads
  WHERE id = lead_uuid;

  IF lead_company_id IS NULL THEN
    RAISE EXCEPTION 'Lead not found or no company associated';
  END IF;

  -- Verify user has access to this company
  IF NOT EXISTS (
    SELECT 1 FROM public.user_roles ur
    WHERE ur.user_id = user_uuid
    AND ur.company_id = lead_company_id
  ) THEN
    RAISE EXCEPTION 'User does not have access to this company';
  END IF;

  -- Delete the answer status record
  DELETE FROM public.lead_answer_status
  WHERE lead_id = lead_uuid;

  RETURN TRUE;
END;
$$;
