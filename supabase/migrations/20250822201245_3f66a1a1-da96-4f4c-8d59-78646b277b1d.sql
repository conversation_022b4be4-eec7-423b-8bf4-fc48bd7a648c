-- Create a default company for testing
INSERT INTO public.companies (id, name, domain, twilio_account_sid, twilio_auth_token, twilio_phone_number)
VALUES (
  'default-company-uuid'::uuid,
  'Default Company', 
  'example.com',
  NULL,
  NULL,
  NULL
) ON CONFLICT DO NOTHING;

-- Create super admin role for the current user (replace with actual user ID)
-- You'll need to replace 'YOUR_USER_ID_HERE' with your actual user ID from auth.users
INSERT INTO public.user_roles (user_id, role, company_id)
VALUES (
  'YOUR_USER_ID_HERE'::uuid,  -- Replace this with your actual user ID
  'super_admin'::app_role,
  'default-company-uuid'::uuid
) ON CONFLICT (user_id, role) DO NOTHING;

-- Alternative: If you want to make the first user in the system a super admin automatically
-- Uncomment the following and comment out the above INSERT:
/*
INSERT INTO public.user_roles (user_id, role, company_id)
SELECT 
  id as user_id,
  'super_admin'::app_role as role,
  'default-company-uuid'::uuid as company_id
FROM auth.users 
WHERE email = 'YOUR_EMAIL_HERE'  -- Replace with your email
LIMIT 1
ON CONFLICT (user_id, role) DO NOTHING;
*/