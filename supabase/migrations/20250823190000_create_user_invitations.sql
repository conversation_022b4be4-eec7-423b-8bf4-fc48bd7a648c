-- Create user invitations table for user management
CREATE TABLE IF NOT EXISTS public.user_invitations (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT NOT NULL,
  full_name TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('admin', 'user')),
  company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
  invited_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired')),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Ensure unique email per company
  UNIQUE(email, company_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_invitations_company_status 
ON public.user_invitations(company_id, status, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_user_invitations_email 
ON public.user_invitations(email);

CREATE INDEX IF NOT EXISTS idx_user_invitations_expires 
ON public.user_invitations(expires_at) 
WHERE status = 'pending';

-- Enable RLS
ALTER TABLE public.user_invitations ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view invitations for their company" ON public.user_invitations
  FOR SELECT USING (
    company_id IN (
      SELECT company_id FROM public.user_roles 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Admins can create invitations for their company" ON public.user_invitations
  FOR INSERT WITH CHECK (
    company_id IN (
      SELECT company_id FROM public.user_roles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Admins can update invitations for their company" ON public.user_invitations
  FOR UPDATE USING (
    company_id IN (
      SELECT company_id FROM public.user_roles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Admins can delete invitations for their company" ON public.user_invitations
  FOR DELETE USING (
    company_id IN (
      SELECT company_id FROM public.user_roles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Function to automatically expire old invitations
CREATE OR REPLACE FUNCTION expire_old_invitations()
RETURNS void AS $$
BEGIN
  UPDATE public.user_invitations 
  SET status = 'expired', updated_at = NOW()
  WHERE status = 'pending' 
    AND expires_at < NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle user signup from invitation
CREATE OR REPLACE FUNCTION handle_invitation_signup()
RETURNS trigger AS $$
DECLARE
  invitation_record RECORD;
BEGIN
  -- Check if user signed up with invitation data
  IF NEW.raw_user_meta_data ? 'invitation_id' THEN
    -- Get invitation details
    SELECT * INTO invitation_record
    FROM public.user_invitations
    WHERE id = (NEW.raw_user_meta_data->>'invitation_id')::UUID
      AND status = 'pending'
      AND expires_at > NOW();
    
    IF FOUND THEN
      -- Create user role
      INSERT INTO public.user_roles (
        user_id,
        company_id,
        role,
        created_by
      ) VALUES (
        NEW.id,
        invitation_record.company_id,
        invitation_record.role,
        invitation_record.invited_by
      );
      
      -- Mark invitation as accepted
      UPDATE public.user_invitations
      SET status = 'accepted', updated_at = NOW()
      WHERE id = invitation_record.id;
      
      -- Update user metadata
      UPDATE auth.users
      SET raw_user_meta_data = jsonb_build_object(
        'full_name', invitation_record.full_name,
        'company_id', invitation_record.company_id
      )
      WHERE id = NEW.id;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for handling invitation signups
DROP TRIGGER IF EXISTS on_auth_user_created_invitation ON auth.users;
CREATE TRIGGER on_auth_user_created_invitation
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_invitation_signup();

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_invitations TO authenticated;
GRANT EXECUTE ON FUNCTION expire_old_invitations() TO authenticated;

-- Add comments
COMMENT ON TABLE public.user_invitations IS 'User invitations for company access';
COMMENT ON FUNCTION expire_old_invitations() IS 'Automatically expire old pending invitations';
COMMENT ON FUNCTION handle_invitation_signup() IS 'Handle user signup from invitation link';
