-- Fix companies table schema by adding missing columns
-- This migration adds the missing email, phone, and address columns that are expected by the application

-- Add missing columns to companies table
ALTER TABLE public.companies 
ADD COLUMN IF NOT EXISTS email TEXT,
ADD COLUMN IF NOT EXISTS phone TEXT,
ADD COLUMN IF NOT EXISTS address TEXT;

-- Update existing companies to have default values for new columns if they don't exist
UPDATE public.companies 
SET 
  email = COALESCE(email, name || '@example.com'),
  phone = COALESCE(phone, '+972-50-000-0000'),
  address = COALESCE(address, 'Address not provided')
WHERE email IS NULL OR phone IS NULL OR address IS NULL;

-- Add constraints to ensure data quality
ALTER TABLE public.companies 
ADD CONSTRAINT companies_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Create index for email lookups
CREATE INDEX IF NOT EXISTS idx_companies_email ON public.companies(email);

-- Update RLS policies to ensure the new columns are properly handled
-- The existing policies should already cover these columns since they use FOR ALL

-- Add a comment to document the schema
COMMENT ON COLUMN public.companies.email IS 'Company contact email address';
COMMENT ON COLUMN public.companies.phone IS 'Company contact phone number';
COMMENT ON COLUMN public.companies.address IS 'Company physical address';
