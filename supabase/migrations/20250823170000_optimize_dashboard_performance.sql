-- Optimize Dashboard Performance for 100+ Companies
-- This migration creates optimized queries and indexes for scalability

-- Create optimized dashboard metrics function
CREATE OR REPLACE FUNCTION public.get_dashboard_metrics(
  p_company_id UUID,
  p_date_from TIMESTAMP WITH TIME ZONE,
  p_date_to TIMESTAMP WITH TIME ZONE
)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'leads', (
      SELECT json_build_object(
        'total', COUNT(*),
        'closed', COUNT(*) FILTER (WHERE status = 'לקוח סגור'),
        'close_rate', CASE 
          WHEN COUNT(*) > 0 THEN (COUNT(*) FILTER (WHERE status = 'לקוח סגור') * 100.0 / COUNT(*))
          ELSE 0 
        END,
        'total_value', COALESCE(SUM(value), 0),
        'closed_deals_revenue', COALESCE(SUM(value) FILTER (WHERE status = 'לקוח סגור'), 0),
        'by_status', COALESCE(
          json_agg(
            json_build_object(
              'status', status, 
              'count', status_count,
              'value', status_value
            )
          ) FILTER (WHERE status IS NOT NULL), 
          '[]'::json
        )
      )
      FROM (
        SELECT 
          status, 
          COUNT(*) as status_count,
          COALESCE(SUM(value), 0) as status_value
        FROM leads 
        WHERE company_id = p_company_id 
        AND created_at BETWEEN p_date_from AND p_date_to
        GROUP BY status
      ) status_aggregates
    ),
    'cases', (
      SELECT json_build_object(
        'total', COUNT(*),
        'active', COUNT(*) FILTER (WHERE status != 'סגור'),
        'completed', COUNT(*) FILTER (WHERE status = 'סגור'),
        'by_type', COALESCE(
          json_agg(
            json_build_object(
              'type', COALESCE(case_type_name, 'ללא סוג'), 
              'count', type_count,
              'value', type_value
            )
          ) FILTER (WHERE case_type_name IS NOT NULL OR type_count > 0), 
          '[]'::json
        ),
        'by_status', COALESCE(
          json_agg(
            json_build_object(
              'status', status, 
              'count', status_count
            )
          ) FILTER (WHERE status IS NOT NULL), 
          '[]'::json
        )
      )
      FROM (
        SELECT 
          ct.name as case_type_name, 
          c.status,
          COUNT(*) as type_count,
          COUNT(*) as status_count,
          COALESCE(SUM(c.value), 0) as type_value
        FROM cases c
        LEFT JOIN case_types ct ON c.case_type_id = ct.id
        WHERE c.company_id = p_company_id 
        AND c.created_at BETWEEN p_date_from AND p_date_to
        GROUP BY GROUPING SETS ((ct.name), (c.status))
      ) case_aggregates
    ),
    'time_entries', (
      SELECT json_build_object(
        'total_hours', COALESCE(SUM(duration), 0) / 60.0,
        'billable_hours', COALESCE(SUM(duration) FILTER (WHERE hourly_rate > 0), 0) / 60.0,
        'total_cost', COALESCE(SUM(total_cost), 0),
        'average_hourly_rate', CASE 
          WHEN SUM(duration) FILTER (WHERE hourly_rate > 0) > 0 
          THEN SUM(total_cost) / (SUM(duration) FILTER (WHERE hourly_rate > 0) / 60.0)
          ELSE 0 
        END,
        'utilization_rate', CASE 
          WHEN SUM(duration) > 0 
          THEN (SUM(duration) FILTER (WHERE hourly_rate > 0) * 100.0 / SUM(duration))
          ELSE 0 
        END
      )
      FROM case_time_entries
      WHERE company_id = p_company_id 
      AND created_at BETWEEN p_date_from AND p_date_to
    ),
    'revenue_by_month', (
      SELECT COALESCE(json_agg(
        json_build_object(
          'month', month_year,
          'revenue', monthly_revenue,
          'hours', monthly_hours,
          'closed_deals', monthly_deals
        ) ORDER BY month_year
      ), '[]'::json)
      FROM (
        SELECT 
          to_char(created_at, 'YYYY-MM') as month_year,
          COALESCE(SUM(value) FILTER (WHERE status = 'לקוח סגור'), 0) as monthly_revenue,
          0 as monthly_hours,
          COUNT(*) FILTER (WHERE status = 'לקוח סגור') as monthly_deals
        FROM leads
        WHERE company_id = p_company_id 
        AND created_at BETWEEN p_date_from AND p_date_to
        GROUP BY to_char(created_at, 'YYYY-MM')
        
        UNION ALL
        
        SELECT 
          to_char(created_at, 'YYYY-MM') as month_year,
          0 as monthly_revenue,
          COALESCE(SUM(duration), 0) / 60.0 as monthly_hours,
          0 as monthly_deals
        FROM case_time_entries
        WHERE company_id = p_company_id 
        AND created_at BETWEEN p_date_from AND p_date_to
        GROUP BY to_char(created_at, 'YYYY-MM')
      ) monthly_data
      GROUP BY month_year
    )
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create performance-optimized indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leads_company_created_status 
ON public.leads(company_id, created_at DESC, status) 
WHERE created_at > NOW() - INTERVAL '1 year';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cases_company_created_status 
ON public.cases(company_id, created_at DESC, status) 
WHERE created_at > NOW() - INTERVAL '1 year';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_time_entries_company_created_rate 
ON public.case_time_entries(company_id, created_at DESC, hourly_rate) 
WHERE created_at > NOW() - INTERVAL '1 year';

-- Partial indexes for active records only
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leads_active_company 
ON public.leads(company_id, created_at DESC) 
WHERE status != 'לקוח סגור' AND created_at > NOW() - INTERVAL '1 year';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cases_active_company 
ON public.cases(company_id, created_at DESC) 
WHERE status != 'סגור' AND created_at > NOW() - INTERVAL '1 year';

-- Index for case type joins
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cases_company_type 
ON public.cases(company_id, case_type_id, created_at DESC) 
WHERE created_at > NOW() - INTERVAL '1 year';

-- Composite index for time entries with cost calculations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_time_entries_billable 
ON public.case_time_entries(company_id, created_at DESC, duration, total_cost) 
WHERE hourly_rate > 0 AND created_at > NOW() - INTERVAL '1 year';

-- Add function for company statistics (for super admin)
CREATE OR REPLACE FUNCTION public.get_company_statistics()
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_companies', COUNT(*),
    'active_companies', COUNT(*) FILTER (WHERE status = 'active'),
    'companies_by_plan', json_agg(
      json_build_object(
        'plan', subscription_plan,
        'count', plan_count
      )
    ),
    'total_users', (SELECT COUNT(*) FROM user_roles WHERE role != 'super_admin'),
    'companies_created_this_month', COUNT(*) FILTER (
      WHERE created_at >= date_trunc('month', CURRENT_DATE)
    )
  ) INTO result
  FROM (
    SELECT 
      status,
      subscription_plan,
      created_at,
      COUNT(*) OVER (PARTITION BY subscription_plan) as plan_count
    FROM companies
  ) company_stats;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_dashboard_metrics(UUID, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_company_statistics() TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION public.get_dashboard_metrics IS 'Optimized single-query dashboard metrics for company performance';
COMMENT ON FUNCTION public.get_company_statistics IS 'Super admin company statistics aggregation';

-- Create materialized view for heavy aggregations (optional, for future use)
CREATE MATERIALIZED VIEW IF NOT EXISTS public.company_daily_metrics AS
SELECT 
  company_id,
  DATE(created_at) as metric_date,
  'leads' as metric_type,
  COUNT(*) as daily_count,
  COALESCE(SUM(value), 0) as daily_value
FROM leads
WHERE created_at > NOW() - INTERVAL '90 days'
GROUP BY company_id, DATE(created_at)

UNION ALL

SELECT 
  company_id,
  DATE(created_at) as metric_date,
  'cases' as metric_type,
  COUNT(*) as daily_count,
  COALESCE(SUM(value), 0) as daily_value
FROM cases
WHERE created_at > NOW() - INTERVAL '90 days'
GROUP BY company_id, DATE(created_at)

UNION ALL

SELECT 
  company_id,
  DATE(created_at) as metric_date,
  'time_entries' as metric_type,
  COUNT(*) as daily_count,
  COALESCE(SUM(total_cost), 0) as daily_value
FROM case_time_entries
WHERE created_at > NOW() - INTERVAL '90 days'
GROUP BY company_id, DATE(created_at);

-- Index the materialized view
CREATE INDEX IF NOT EXISTS idx_company_daily_metrics_company_date 
ON public.company_daily_metrics(company_id, metric_date DESC, metric_type);

-- Refresh function for materialized view
CREATE OR REPLACE FUNCTION public.refresh_company_metrics()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY public.company_daily_metrics;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Schedule daily refresh (requires pg_cron extension)
-- SELECT cron.schedule('refresh-company-metrics', '0 2 * * *', 'SELECT public.refresh_company_metrics();');
