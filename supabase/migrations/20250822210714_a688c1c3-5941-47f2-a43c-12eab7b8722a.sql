-- Add GreenAPI credentials to companies table
ALTER TABLE public.companies 
ADD COLUMN green_api_instance_id TEXT,
ADD COLUMN green_api_token TEXT;

-- Create whatsapp_conversations table
CREATE TABLE public.whatsapp_conversations (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  company_id UUID NOT NULL,
  lead_id UUID,
  phone_number TEXT NOT NULL,
  green_api_chat_id TEXT,
  contact_name TEXT,
  last_message TEXT,
  last_message_timestamp TIMESTAMP WITH TIME ZONE,
  unread_count INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create whatsapp_messages table
CREATE TABLE public.whatsapp_messages (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  conversation_id UUID NOT NULL REFERENCES public.whatsapp_conversations(id) ON DELETE CASCADE,
  green_api_message_id TEXT,
  content TEXT NOT NULL,
  message_type TEXT NOT NULL DEFAULT 'text',
  sender_type TEXT NOT NULL CHECK (sender_type IN ('outgoing', 'incoming')),
  status TEXT NOT NULL DEFAULT 'sent',
  media_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on new tables
ALTER TABLE public.whatsapp_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.whatsapp_messages ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for whatsapp_conversations
CREATE POLICY "Users can view company conversations" 
ON public.whatsapp_conversations 
FOR SELECT 
USING (company_id = get_user_company());

CREATE POLICY "Users can create company conversations" 
ON public.whatsapp_conversations 
FOR INSERT 
WITH CHECK (company_id = get_user_company());

CREATE POLICY "Users can update company conversations" 
ON public.whatsapp_conversations 
FOR UPDATE 
USING (company_id = get_user_company());

-- Create RLS policies for whatsapp_messages
CREATE POLICY "Users can view company messages" 
ON public.whatsapp_messages 
FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.whatsapp_conversations 
    WHERE id = conversation_id AND company_id = get_user_company()
  )
);

CREATE POLICY "Users can create company messages" 
ON public.whatsapp_messages 
FOR INSERT 
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.whatsapp_conversations 
    WHERE id = conversation_id AND company_id = get_user_company()
  )
);

-- Create indexes for better performance
CREATE INDEX idx_whatsapp_conversations_company_id ON public.whatsapp_conversations(company_id);
CREATE INDEX idx_whatsapp_conversations_lead_id ON public.whatsapp_conversations(lead_id);
CREATE INDEX idx_whatsapp_conversations_phone_number ON public.whatsapp_conversations(phone_number);
CREATE INDEX idx_whatsapp_messages_conversation_id ON public.whatsapp_messages(conversation_id);
CREATE INDEX idx_whatsapp_messages_created_at ON public.whatsapp_messages(created_at);

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_whatsapp_conversations_updated_at
BEFORE UPDATE ON public.whatsapp_conversations
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Enable realtime for live updates
ALTER PUBLICATION supabase_realtime ADD TABLE public.whatsapp_conversations;
ALTER PUBLICATION supabase_realtime ADD TABLE public.whatsapp_messages;