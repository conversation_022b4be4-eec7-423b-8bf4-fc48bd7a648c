-- Update dashboard metrics to calculate income from cases instead of leads
-- This migration updates the get_dashboard_metrics function to use completed cases for revenue calculation

CREATE OR REPLACE FUNCTION public.get_dashboard_metrics(
  p_company_id UUID,
  p_date_from TIMESTAMP WITH TIME ZONE,
  p_date_to TIMESTAMP WITH TIME ZONE
)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  -- Build comprehensive dashboard metrics with nested structure
  SELECT json_build_object(
    'leads', (
      SELECT json_build_object(
        'total', COUNT(*),
        'closed', COUNT(*) FILTER (WHERE status = 'לקוח סגור'),
        'close_rate', CASE
          WHEN COUNT(*) > 0 THEN (COUNT(*) FILTER (WHERE status = 'לקוח סגור') * 100.0 / COUNT(*))
          ELSE 0
        END,
        'total_value', COALESCE(SUM(value), 0),
        'closed_deals_revenue', (
          SELECT COALESCE(SUM(c.value), 0)
          FROM cases c
          WHERE c.company_id = p_company_id
          AND c.created_at BETWEEN p_date_from AND p_date_to
          AND c.status = 'סגור'
        ),
        'by_status', COALESCE(
          json_agg(
            json_build_object(
              'status', status,
              'count', status_count,
              'value', status_value
            )
          ) FILTER (WHERE status IS NOT NULL),
          '[]'::json
        )
      )
      FROM (
        SELECT
          status,
          COUNT(*) as status_count,
          COALESCE(SUM(value), 0) as status_value
        FROM leads
        WHERE company_id = p_company_id
        AND created_at BETWEEN p_date_from AND p_date_to
        GROUP BY status
      ) status_aggregates
    ),
    'cases', (
      SELECT json_build_object(
        'total', COUNT(*),
        'active', COUNT(*) FILTER (WHERE status != 'סגור'),
        'completed', COUNT(*) FILTER (WHERE status = 'סגור'),
        'by_type', COALESCE(
          json_agg(
            json_build_object(
              'type', COALESCE(case_type_name, 'ללא סוג'),
              'count', type_count,
              'value', type_value
            )
          ) FILTER (WHERE case_type_name IS NOT NULL OR type_count > 0),
          '[]'::json
        ),
        'by_status', COALESCE(
          json_agg(
            json_build_object(
              'status', status,
              'count', status_count
            )
          ) FILTER (WHERE status IS NOT NULL),
          '[]'::json
        )
      )
      FROM (
        SELECT
          c.status,
          ct.name as case_type_name,
          COUNT(*) as status_count,
          COUNT(*) as type_count,
          COALESCE(SUM(c.value), 0) as type_value
        FROM cases c
        LEFT JOIN case_types ct ON c.case_type_id = ct.id
        WHERE c.company_id = p_company_id
        AND c.created_at BETWEEN p_date_from AND p_date_to
        GROUP BY c.status, ct.name
      ) case_aggregates
    ),
    'time_entries', (
      SELECT json_build_object(
        'total_hours', COALESCE(SUM(duration), 0) / 60.0,
        'billable_hours', COALESCE(SUM(duration) FILTER (WHERE hourly_rate > 0), 0) / 60.0,
        'total_cost', COALESCE(SUM(total_cost), 0),
        'average_hourly_rate', CASE
          WHEN SUM(duration) FILTER (WHERE hourly_rate > 0) > 0 THEN
            SUM(total_cost) / (SUM(duration) FILTER (WHERE hourly_rate > 0) / 60.0)
          ELSE 0
        END,
        'utilization_rate', CASE
          WHEN SUM(duration) > 0 THEN
            (SUM(duration) FILTER (WHERE hourly_rate > 0) * 100.0 / SUM(duration))
          ELSE 0
        END
      )
      FROM case_time_entries
      WHERE company_id = p_company_id
      AND created_at BETWEEN p_date_from AND p_date_to
    ),
    'revenue_by_month', (
      SELECT COALESCE(json_agg(
        json_build_object(
          'month', month_year,
          'revenue', monthly_revenue,
          'hours', monthly_hours,
          'closed_deals', monthly_deals
        ) ORDER BY month_year
      ), '[]'::json)
      FROM (
        SELECT
          to_char(created_at, 'YYYY-MM') as month_year,
          COALESCE(SUM(value) FILTER (WHERE status = 'סגור'), 0) as monthly_revenue,
          0 as monthly_hours,
          COUNT(*) FILTER (WHERE status = 'סגור') as monthly_deals
        FROM cases
        WHERE company_id = p_company_id
        AND created_at BETWEEN p_date_from AND p_date_to
        GROUP BY to_char(created_at, 'YYYY-MM')

        UNION ALL

        SELECT
          to_char(created_at, 'YYYY-MM') as month_year,
          0 as monthly_revenue,
          COALESCE(SUM(duration), 0) / 60.0 as monthly_hours,
          0 as monthly_deals
        FROM case_time_entries
        WHERE company_id = p_company_id
        AND created_at BETWEEN p_date_from AND p_date_to
        GROUP BY to_char(created_at, 'YYYY-MM')
      ) monthly_data
      GROUP BY month_year
    )
  ) INTO result;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_dashboard_metrics(UUID, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE) TO authenticated;
