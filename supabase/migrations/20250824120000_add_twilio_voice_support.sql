-- Add Twilio Voice support to companies table
-- This migration adds the necessary Twilio credentials for Voice SDK integration

-- Add Twilio Voice specific columns to companies table
ALTER TABLE public.companies 
ADD COLUMN IF NOT EXISTS twilio_account_sid TEXT,
ADD COLUMN IF NOT EXISTS twilio_api_key TEXT,
ADD COLUMN IF NOT EXISTS twilio_api_secret TEXT,
ADD COLUMN IF NOT EXISTS twilio_twiml_app_sid TEXT;

-- Add comments for documentation
COMMENT ON COLUMN public.companies.twilio_account_sid IS 'Twilio Account SID for Voice API';
COMMENT ON COLUMN public.companies.twilio_api_key IS 'Twilio API Key for Voice SDK authentication';
COMMENT ON COLUMN public.companies.twilio_api_secret IS 'Twilio API Secret for Voice SDK authentication';
COMMENT ON COLUMN public.companies.twilio_twiml_app_sid IS 'Twilio TwiML Application SID for Voice calls';

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_companies_twilio_account ON public.companies(twilio_account_sid);

-- Create a function to validate <PERSON>wi<PERSON> credentials
CREATE OR REPLACE FUNCTION public.validate_twilio_credentials(
  account_sid TEXT,
  api_key TEXT,
  api_secret TEXT,
  twiml_app_sid TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
  -- Basic validation - check that all required fields are present and have correct format
  IF account_sid IS NULL OR account_sid = '' THEN
    RETURN FALSE;
  END IF;
  
  IF api_key IS NULL OR api_key = '' THEN
    RETURN FALSE;
  END IF;
  
  IF api_secret IS NULL OR api_secret = '' THEN
    RETURN FALSE;
  END IF;
  
  IF twiml_app_sid IS NULL OR twiml_app_sid = '' THEN
    RETURN FALSE;
  END IF;
  
  -- Check format - Twilio SIDs start with specific prefixes
  IF NOT (account_sid ~ '^AC[a-f0-9]{32}$') THEN
    RETURN FALSE;
  END IF;
  
  IF NOT (api_key ~ '^SK[a-f0-9]{32}$') THEN
    RETURN FALSE;
  END IF;
  
  IF NOT (twiml_app_sid ~ '^AP[a-f0-9]{32}$') THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$$;

-- Create a function to update Twilio credentials securely
CREATE OR REPLACE FUNCTION public.update_company_twilio_credentials(
  company_uuid UUID,
  user_uuid UUID,
  account_sid TEXT,
  api_key TEXT,
  api_secret TEXT,
  twiml_app_sid TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Verify user has admin access to this company
  IF NOT EXISTS (
    SELECT 1 FROM public.user_roles ur
    WHERE ur.user_id = user_uuid
    AND ur.company_id = company_uuid
    AND ur.role IN ('super_admin', 'company_admin')
  ) THEN
    RAISE EXCEPTION 'User does not have admin access to this company';
  END IF;

  -- Validate credentials format
  IF NOT public.validate_twilio_credentials(account_sid, api_key, api_secret, twiml_app_sid) THEN
    RAISE EXCEPTION 'Invalid Twilio credentials format';
  END IF;

  -- Update the company record
  UPDATE public.companies
  SET 
    twilio_account_sid = account_sid,
    twilio_api_key = api_key,
    twilio_api_secret = api_secret,
    twilio_twiml_app_sid = twiml_app_sid,
    updated_at = NOW()
  WHERE id = company_uuid;

  RETURN TRUE;
END;
$$;

-- Create a function to check if company has Twilio configured
CREATE OR REPLACE FUNCTION public.company_has_twilio_configured(company_uuid UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  has_config BOOLEAN := FALSE;
BEGIN
  SELECT 
    (twilio_account_sid IS NOT NULL AND twilio_account_sid != '' AND
     twilio_api_key IS NOT NULL AND twilio_api_key != '' AND
     twilio_api_secret IS NOT NULL AND twilio_api_secret != '' AND
     twilio_twiml_app_sid IS NOT NULL AND twilio_twiml_app_sid != '')
  INTO has_config
  FROM public.companies
  WHERE id = company_uuid;

  RETURN COALESCE(has_config, FALSE);
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.validate_twilio_credentials TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_company_twilio_credentials TO authenticated;
GRANT EXECUTE ON FUNCTION public.company_has_twilio_configured TO authenticated;
