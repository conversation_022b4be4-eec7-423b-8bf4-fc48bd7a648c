-- Update the companies table with GreenAPI credentials
-- This assumes there's at least one company record to update
-- In a real scenario, this would be done through the admin interface

-- For now, let's update the first company record with the provided credentials
-- In production, this should be done through the admin interface
UPDATE companies 
SET 
  green_api_instance_id = '7105248903',
  green_api_token = 'cb50feb89aa84b3695a297073664d44f81dbefb0259b445cb7',
  updated_at = now()
WHERE id = (SELECT id FROM companies LIMIT 1);