-- Marketing Automation System
-- This migration creates the complete database structure for marketing workflows

-- Create workflows table
CREATE TABLE public.workflows (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  
  -- Trigger configuration
  trigger_type VARCHAR(50) NOT NULL CHECK (trigger_type IN ('lead_status_change', 'case_status_change')),
  trigger_config JSONB NOT NULL, -- Stores from_status, to_status, etc.
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  CONSTRAINT workflows_name_company_unique UNIQUE (company_id, name)
);

-- Create workflow_steps table
CREATE TABLE public.workflow_steps (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  workflow_id UUID NOT NULL REFERENCES public.workflows(id) ON DELETE CASCADE,
  step_order INTEGER NOT NULL,
  step_type VARCHAR(50) NOT NULL CHECK (step_type IN ('send_whatsapp', 'wait', 'update_lead_status', 'update_case_status', 'create_case')),
  step_config JSONB NOT NULL, -- Stores step-specific configuration
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT workflow_steps_order_unique UNIQUE (workflow_id, step_order)
);

-- Create workflow_executions table (tracks each time a workflow runs)
CREATE TABLE public.workflow_executions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  workflow_id UUID NOT NULL REFERENCES public.workflows(id) ON DELETE CASCADE,
  company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
  
  -- Context of execution
  trigger_entity_type VARCHAR(20) NOT NULL CHECK (trigger_entity_type IN ('lead', 'case')),
  trigger_entity_id UUID NOT NULL, -- ID of the lead or case that triggered this
  
  -- Execution status
  status VARCHAR(20) DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'paused')),
  current_step_order INTEGER DEFAULT 1,
  
  -- Timing
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  next_execution_at TIMESTAMP WITH TIME ZONE, -- For wait steps
  
  -- Error handling
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create workflow_execution_logs table (detailed logs for each step)
CREATE TABLE public.workflow_execution_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  execution_id UUID NOT NULL REFERENCES public.workflow_executions(id) ON DELETE CASCADE,
  step_id UUID NOT NULL REFERENCES public.workflow_steps(id) ON DELETE CASCADE,
  
  -- Step execution details
  step_order INTEGER NOT NULL,
  step_type VARCHAR(50) NOT NULL,
  status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'running', 'completed', 'failed', 'retrying')),
  
  -- Execution data
  input_data JSONB, -- Data passed to the step
  output_data JSONB, -- Data returned from the step
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  
  -- Timing
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_workflows_company_id ON public.workflows(company_id);
CREATE INDEX idx_workflows_active ON public.workflows(is_active) WHERE is_active = true;
CREATE INDEX idx_workflow_steps_workflow_id ON public.workflow_steps(workflow_id);
CREATE INDEX idx_workflow_steps_order ON public.workflow_steps(workflow_id, step_order);
CREATE INDEX idx_workflow_executions_workflow_id ON public.workflow_executions(workflow_id);
CREATE INDEX idx_workflow_executions_status ON public.workflow_executions(status);
CREATE INDEX idx_workflow_executions_next_execution ON public.workflow_executions(next_execution_at) WHERE next_execution_at IS NOT NULL;
CREATE INDEX idx_workflow_executions_entity ON public.workflow_executions(trigger_entity_type, trigger_entity_id);
CREATE INDEX idx_workflow_execution_logs_execution_id ON public.workflow_execution_logs(execution_id);

-- Create RLS policies
ALTER TABLE public.workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.workflow_steps ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.workflow_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.workflow_execution_logs ENABLE ROW LEVEL SECURITY;

-- Workflows policies
CREATE POLICY "Users can view workflows from their company" ON public.workflows
  FOR SELECT USING (
    company_id IN (
      SELECT company_id FROM public.user_roles 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Company admins can manage workflows" ON public.workflows
  FOR ALL USING (
    company_id IN (
      SELECT company_id FROM public.user_roles 
      WHERE user_id = auth.uid() 
      AND role IN ('company_admin', 'super_admin')
    )
  );

-- Workflow steps policies
CREATE POLICY "Users can view workflow steps from their company" ON public.workflow_steps
  FOR SELECT USING (
    workflow_id IN (
      SELECT id FROM public.workflows 
      WHERE company_id IN (
        SELECT company_id FROM public.user_roles 
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Company admins can manage workflow steps" ON public.workflow_steps
  FOR ALL USING (
    workflow_id IN (
      SELECT id FROM public.workflows 
      WHERE company_id IN (
        SELECT company_id FROM public.user_roles 
        WHERE user_id = auth.uid() 
        AND role IN ('company_admin', 'super_admin')
      )
    )
  );

-- Workflow executions policies
CREATE POLICY "Users can view executions from their company" ON public.workflow_executions
  FOR SELECT USING (
    company_id IN (
      SELECT company_id FROM public.user_roles 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "System can manage executions" ON public.workflow_executions
  FOR ALL USING (true); -- System needs full access for execution engine

-- Workflow execution logs policies
CREATE POLICY "Users can view execution logs from their company" ON public.workflow_execution_logs
  FOR SELECT USING (
    execution_id IN (
      SELECT id FROM public.workflow_executions 
      WHERE company_id IN (
        SELECT company_id FROM public.user_roles 
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "System can manage execution logs" ON public.workflow_execution_logs
  FOR ALL USING (true); -- System needs full access for execution engine

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_workflows_updated_at 
  BEFORE UPDATE ON public.workflows 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workflow_executions_updated_at 
  BEFORE UPDATE ON public.workflow_executions 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.workflows TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.workflow_steps TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.workflow_executions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.workflow_execution_logs TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE public.workflows IS 'Marketing automation workflows with triggers and actions';
COMMENT ON TABLE public.workflow_steps IS 'Individual steps within workflows (actions to perform)';
COMMENT ON TABLE public.workflow_executions IS 'Runtime instances of workflow executions';
COMMENT ON TABLE public.workflow_execution_logs IS 'Detailed logs for each step execution';

COMMENT ON COLUMN public.workflows.trigger_config IS 'JSON config: {"from_status": "any|specific", "to_status": "specific_status"}';
COMMENT ON COLUMN public.workflow_steps.step_config IS 'JSON config specific to each step type';
COMMENT ON COLUMN public.workflow_executions.trigger_entity_type IS 'Whether this execution was triggered by a lead or case';
COMMENT ON COLUMN public.workflow_executions.trigger_entity_id IS 'ID of the lead or case that triggered this execution';
