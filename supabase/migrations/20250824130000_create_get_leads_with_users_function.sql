-- Create function to get leads with user information
-- This function replaces the missing get_leads_with_users RPC function

CREATE OR REPLACE FUNCTION public.get_leads_with_users(company_uuid UUID)
RETURNS TABLE (
  id UUID,
  full_name TEXT,
  phone TEXT,
  email TEXT,
  source TEXT,
  status TEXT,
  value NUMERIC,
  notes TEXT,
  user_id UUID,
  company_id UUID,
  assigned_user_id UUID,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  answer_status TEXT,
  attempt_number INTEGER,
  assigned_user_email TEXT,
  assigned_user_name TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Check if user has access to this company
  IF NOT EXISTS (
    SELECT 1 FROM public.user_roles ur
    WHERE ur.user_id = auth.uid()
    AND (ur.company_id = company_uuid OR ur.role = 'super_admin')
  ) THEN
    RAISE EXCEPTION 'User does not have access to this company';
  END IF;

  -- Return leads with user information
  RETURN QUERY
  SELECT 
    l.id,
    l.full_name,
    l.phone,
    l.email,
    l.source,
    l.status,
    l.value,
    l.notes,
    l.user_id,
    l.company_id,
    l.assigned_user_id,
    l.created_at,
    l.updated_at,
    las.answer_status,
    las.attempt_number,
    au.email as assigned_user_email,
    (au.raw_user_meta_data->>'full_name')::TEXT as assigned_user_name
  FROM public.leads l
  LEFT JOIN public.lead_answer_status las ON l.id = las.lead_id
  LEFT JOIN public.user_roles ur ON l.assigned_user_id = ur.user_id AND ur.company_id = l.company_id
  LEFT JOIN auth.users au ON ur.user_id = au.id
  WHERE l.company_id = company_uuid
  ORDER BY l.created_at DESC;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_leads_with_users(UUID) TO authenticated;
