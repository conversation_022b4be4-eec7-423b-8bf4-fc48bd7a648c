-- Ensure companies table has all required columns for the application
-- This migration ensures all columns expected by the application exist

-- Add missing columns to companies table (using IF NOT EXISTS to avoid errors)
ALTER TABLE public.companies 
ADD COLUMN IF NOT EXISTS email TEXT,
ADD COLUMN IF NOT EXISTS phone TEXT,
ADD COLUMN IF NOT EXISTS address TEXT,
ADD COLUMN IF NOT EXISTS subscription_plan TEXT DEFAULT 'basic',
ADD COLUMN IF NOT EXISTS max_users INTEGER DEFAULT 10,
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active',
ADD COLUMN IF NOT EXISTS settings JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS api_tokens JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS onboarded_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS onboarded_by UUI<PERSON>;

-- Add constraints for data integrity
ALTER TABLE public.companies 
DROP CONSTRAINT IF EXISTS companies_email_format,
ADD CONSTRAINT companies_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE public.companies 
DROP CONSTRAINT IF EXISTS companies_status_check,
ADD CONSTRAINT companies_status_check CHECK (status IN ('active', 'suspended', 'inactive'));

ALTER TABLE public.companies 
DROP CONSTRAINT IF EXISTS companies_subscription_plan_check,
ADD CONSTRAINT companies_subscription_plan_check CHECK (subscription_plan IN ('basic', 'pro', 'enterprise'));

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_companies_email ON public.companies(email);
CREATE INDEX IF NOT EXISTS idx_companies_status ON public.companies(status);
CREATE INDEX IF NOT EXISTS idx_companies_subscription_plan ON public.companies(subscription_plan);
CREATE INDEX IF NOT EXISTS idx_companies_onboarded_by ON public.companies(onboarded_by);

-- Update existing companies to have default values for new columns
UPDATE public.companies 
SET 
  email = COALESCE(email, name || '@example.com'),
  phone = COALESCE(phone, '+972-50-000-0000'),
  address = COALESCE(address, 'Address not provided'),
  subscription_plan = COALESCE(subscription_plan, 'basic'),
  max_users = COALESCE(max_users, 10),
  status = COALESCE(status, 'active'),
  settings = COALESCE(settings, '{}'),
  api_tokens = COALESCE(api_tokens, '{}')
WHERE 
  email IS NULL OR 
  phone IS NULL OR 
  address IS NULL OR 
  subscription_plan IS NULL OR 
  max_users IS NULL OR 
  status IS NULL OR 
  settings IS NULL OR 
  api_tokens IS NULL;

-- Add comments to document the schema
COMMENT ON COLUMN public.companies.email IS 'Company contact email address';
COMMENT ON COLUMN public.companies.phone IS 'Company contact phone number';
COMMENT ON COLUMN public.companies.address IS 'Company physical address';
COMMENT ON COLUMN public.companies.subscription_plan IS 'Company subscription plan (basic, pro, enterprise)';
COMMENT ON COLUMN public.companies.max_users IS 'Maximum number of users allowed for this company';
COMMENT ON COLUMN public.companies.status IS 'Company status (active, suspended, inactive)';
COMMENT ON COLUMN public.companies.settings IS 'Company-specific settings as JSON';
COMMENT ON COLUMN public.companies.api_tokens IS 'API tokens and credentials as JSON';
COMMENT ON COLUMN public.companies.onboarded_at IS 'Timestamp when company was onboarded';
COMMENT ON COLUMN public.companies.onboarded_by IS 'User ID who onboarded this company';

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
