-- Fix RLS policies to prevent access errors
-- This migration addresses the "User does not have access to this company" errors

-- First, let's ensure the helper functions work correctly
CREATE OR REPLACE FUNCTION public.get_user_company_id(user_uuid UUID DEFAULT auth.uid())
RETURNS UUID
LANGUAGE sql
STABLE
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT company_id FROM public.user_roles 
  WHERE user_id = COALESCE(user_uuid, auth.uid())
  LIMIT 1;
$$;

CREATE OR REPLACE FUNCTION public.is_super_admin(user_uuid UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.user_roles 
    WHERE user_id = COALESCE(user_uuid, auth.uid()) AND role = 'super_admin'
  );
$$;

CREATE OR REPLACE FUNCTION public.is_company_admin(user_uuid UUID DEFAULT auth.uid(), company_uuid UUID DEFAULT NULL)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.user_roles 
    WHERE user_id = COALESCE(user_uuid, auth.uid()) 
    AND (company_uuid IS NULL OR company_id = company_uuid)
    AND role = 'company_admin'
  );
$$;

-- Update leads RLS policies to be more permissive for super admins
DROP POLICY IF EXISTS "Users can view company leads" ON public.leads;
DROP POLICY IF EXISTS "Users can create company leads" ON public.leads;
DROP POLICY IF EXISTS "Users can update company leads" ON public.leads;
DROP POLICY IF EXISTS "Users can delete company leads" ON public.leads;
DROP POLICY IF EXISTS "Company members can manage their company leads" ON public.leads;

-- Create new, more robust policies
CREATE POLICY "Users can view leads in their company or super admins can view all" 
ON public.leads FOR SELECT 
USING (
  public.is_super_admin() OR 
  company_id = public.get_user_company_id()
);

CREATE POLICY "Users can create leads in their company" 
ON public.leads FOR INSERT 
WITH CHECK (
  company_id = public.get_user_company_id() AND 
  user_id = auth.uid()
);

CREATE POLICY "Users can update leads in their company or super admins can update all" 
ON public.leads FOR UPDATE 
USING (
  public.is_super_admin() OR 
  company_id = public.get_user_company_id()
);

CREATE POLICY "Users can delete leads in their company or super admins can delete all" 
ON public.leads FOR DELETE 
USING (
  public.is_super_admin() OR 
  company_id = public.get_user_company_id()
);

-- Update lead_answer_status policies
DROP POLICY IF EXISTS "Users can view answer status for their company leads" ON public.lead_answer_status;
DROP POLICY IF EXISTS "Users can create answer status for their company leads" ON public.lead_answer_status;
DROP POLICY IF EXISTS "Users can update answer status for their company leads" ON public.lead_answer_status;

CREATE POLICY "Users can view answer status for their company leads or super admins can view all" 
ON public.lead_answer_status FOR SELECT 
USING (
  public.is_super_admin() OR
  EXISTS (
    SELECT 1 FROM public.user_roles ur 
    WHERE ur.user_id = auth.uid() 
    AND ur.company_id = lead_answer_status.company_id
  )
);

CREATE POLICY "Users can manage answer status for their company leads or super admins can manage all" 
ON public.lead_answer_status FOR ALL 
USING (
  public.is_super_admin() OR
  EXISTS (
    SELECT 1 FROM public.user_roles ur 
    WHERE ur.user_id = auth.uid() 
    AND ur.company_id = lead_answer_status.company_id
  )
);

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.get_user_company_id(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_super_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_company_admin(UUID, UUID) TO authenticated;
