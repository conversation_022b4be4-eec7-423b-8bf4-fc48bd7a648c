-- Create case types table
CREATE TABLE public.case_types (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  hourly_rate NUMERIC(10,2) DEFAULT 0,
  company_id UUID NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create cases table
CREATE TABLE public.cases (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  case_type_id UUID REFERENCES public.case_types(id),
  status TEXT NOT NULL DEFAULT 'בקליטה',
  value NUMERIC(10,2),
  lead_id UUID REFERENCES public.leads(id),
  user_id UUID NOT NULL,
  company_id UUID NOT NULL,
  deadline TIMESTAMP WITH TIME ZONE,
  total_time_logged INTEGER DEFAULT 0, -- in minutes
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create case tasks table
CREATE TABLE public.case_tasks (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  case_id UUID NOT NULL REFERENCES public.cases(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'לא הושלם',
  deadline TIMESTAMP WITH TIME ZONE,
  priority TEXT DEFAULT 'בינוני',
  assigned_to UUID,
  completed_at TIMESTAMP WITH TIME ZONE,
  user_id UUID NOT NULL,
  company_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create case time entries table
CREATE TABLE public.case_time_entries (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  case_id UUID NOT NULL REFERENCES public.cases(id) ON DELETE CASCADE,
  task_id UUID REFERENCES public.case_tasks(id),
  user_id UUID NOT NULL,
  company_id UUID NOT NULL,
  description TEXT,
  start_time TIMESTAMP WITH TIME ZONE,
  end_time TIMESTAMP WITH TIME ZONE,
  duration INTEGER NOT NULL, -- in minutes
  hourly_rate NUMERIC(10,2),
  total_cost NUMERIC(10,2),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create case documents table
CREATE TABLE public.case_documents (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  case_id UUID NOT NULL REFERENCES public.cases(id) ON DELETE CASCADE,
  filename TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_size INTEGER,
  file_type TEXT,
  user_id UUID NOT NULL,
  company_id UUID NOT NULL,
  uploaded_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on all tables
ALTER TABLE public.case_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.case_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.case_time_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.case_documents ENABLE ROW LEVEL SECURITY;

-- RLS policies for case_types
CREATE POLICY "Users can view company case types" ON public.case_types
  FOR SELECT USING (company_id = get_user_company());

CREATE POLICY "Users can create company case types" ON public.case_types
  FOR INSERT WITH CHECK (company_id = get_user_company());

CREATE POLICY "Users can update company case types" ON public.case_types
  FOR UPDATE USING (company_id = get_user_company());

CREATE POLICY "Users can delete company case types" ON public.case_types
  FOR DELETE USING (company_id = get_user_company());

-- RLS policies for cases
CREATE POLICY "Users can view company cases" ON public.cases
  FOR SELECT USING (company_id = get_user_company());

CREATE POLICY "Users can create company cases" ON public.cases
  FOR INSERT WITH CHECK (company_id = get_user_company() AND user_id = auth.uid());

CREATE POLICY "Users can update company cases" ON public.cases
  FOR UPDATE USING (company_id = get_user_company());

CREATE POLICY "Users can delete company cases" ON public.cases
  FOR DELETE USING (company_id = get_user_company());

-- RLS policies for case_tasks
CREATE POLICY "Users can view company case tasks" ON public.case_tasks
  FOR SELECT USING (company_id = get_user_company());

CREATE POLICY "Users can create company case tasks" ON public.case_tasks
  FOR INSERT WITH CHECK (company_id = get_user_company() AND user_id = auth.uid());

CREATE POLICY "Users can update company case tasks" ON public.case_tasks
  FOR UPDATE USING (company_id = get_user_company());

CREATE POLICY "Users can delete company case tasks" ON public.case_tasks
  FOR DELETE USING (company_id = get_user_company());

-- RLS policies for case_time_entries
CREATE POLICY "Users can view company case time entries" ON public.case_time_entries
  FOR SELECT USING (company_id = get_user_company());

CREATE POLICY "Users can create company case time entries" ON public.case_time_entries
  FOR INSERT WITH CHECK (company_id = get_user_company() AND user_id = auth.uid());

CREATE POLICY "Users can update company case time entries" ON public.case_time_entries
  FOR UPDATE USING (company_id = get_user_company());

CREATE POLICY "Users can delete company case time entries" ON public.case_time_entries
  FOR DELETE USING (company_id = get_user_company());

-- RLS policies for case_documents
CREATE POLICY "Users can view company case documents" ON public.case_documents
  FOR SELECT USING (company_id = get_user_company());

CREATE POLICY "Users can create company case documents" ON public.case_documents
  FOR INSERT WITH CHECK (company_id = get_user_company() AND user_id = auth.uid());

CREATE POLICY "Users can update company case documents" ON public.case_documents
  FOR UPDATE USING (company_id = get_user_company());

CREATE POLICY "Users can delete company case documents" ON public.case_documents
  FOR DELETE USING (company_id = get_user_company());

-- Add triggers for updated_at
CREATE TRIGGER update_case_types_updated_at
  BEFORE UPDATE ON public.case_types
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_cases_updated_at
  BEFORE UPDATE ON public.cases
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_case_tasks_updated_at
  BEFORE UPDATE ON public.case_tasks
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Function to sync case value with lead value
CREATE OR REPLACE FUNCTION public.sync_case_lead_value()
RETURNS TRIGGER AS $$
BEGIN
  -- Update lead value when case value changes
  IF TG_OP = 'UPDATE' AND OLD.value IS DISTINCT FROM NEW.value THEN
    UPDATE public.leads 
    SET value = NEW.value 
    WHERE id = NEW.lead_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to sync case value with lead value
CREATE TRIGGER sync_case_lead_value_trigger
  AFTER UPDATE ON public.cases
  FOR EACH ROW
  EXECUTE FUNCTION public.sync_case_lead_value();

-- Function to update case total time
CREATE OR REPLACE FUNCTION public.update_case_total_time()
RETURNS TRIGGER AS $$
BEGIN
  -- Update total time logged for the case
  UPDATE public.cases
  SET total_time_logged = (
    SELECT COALESCE(SUM(duration), 0)
    FROM public.case_time_entries
    WHERE case_id = COALESCE(NEW.case_id, OLD.case_id)
  )
  WHERE id = COALESCE(NEW.case_id, OLD.case_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger to update case total time
CREATE TRIGGER update_case_total_time_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.case_time_entries
  FOR EACH ROW
  EXECUTE FUNCTION public.update_case_total_time();

-- Create storage bucket for case documents
INSERT INTO storage.buckets (id, name, public) VALUES ('case-documents', 'case-documents', false);

-- Storage policies for case documents
CREATE POLICY "Users can view company case documents" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'case-documents' AND
    EXISTS (
      SELECT 1 FROM public.case_documents cd
      WHERE cd.file_path = name AND cd.company_id = get_user_company()
    )
  );

CREATE POLICY "Users can upload company case documents" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'case-documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update company case documents" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'case-documents' AND
    EXISTS (
      SELECT 1 FROM public.case_documents cd
      WHERE cd.file_path = name AND cd.company_id = get_user_company()
    )
  );

CREATE POLICY "Users can delete company case documents" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'case-documents' AND
    EXISTS (
      SELECT 1 FROM public.case_documents cd
      WHERE cd.file_path = name AND cd.company_id = get_user_company()
    )
  );