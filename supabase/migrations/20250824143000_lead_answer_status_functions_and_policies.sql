-- Ensure super admins bypass company checks for lead_answer_status RPCs

-- Create: get_lead_answer_status function to fetch a lead's answer status with access checks
CREATE OR REPLACE FUNCTION public.get_lead_answer_status(
  p_lead_id UUID,
  p_user_id UUID
)
RETURNS public.lead_answer_status
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result_record public.lead_answer_status;
  lead_company_id UUID;
  is_super BOOLEAN;
BEGIN
  -- Determine lead company
  SELECT company_id INTO lead_company_id FROM public.leads WHERE id = p_lead_id;
  IF lead_company_id IS NULL THEN
    RAISE EXCEPTION 'Lead not found or no company associated';
  END IF;

  -- Super admin bypass
  SELECT EXISTS (
    SELECT 1 FROM public.user_roles ur
    WHERE ur.user_id = p_user_id AND ur.role = 'super_admin'
  ) INTO is_super;

  IF NOT is_super THEN
    -- Verify user has access to this company
    IF NOT EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.user_id = p_user_id AND ur.company_id = lead_company_id
    ) THEN
      RAISE EXCEPTION 'User does not have access to this company';
    END IF;
  END IF;

  SELECT * INTO result_record
  FROM public.lead_answer_status
  WHERE lead_id = p_lead_id
  LIMIT 1;

  RETURN result_record;
END;
$$;

-- Update upsert/reset functions to allow super admins bypass
CREATE OR REPLACE FUNCTION public.upsert_lead_answer_status(
  p_lead_id UUID,
  p_user_id UUID,
  p_attempt_number INTEGER,
  p_answer_status TEXT
)
RETURNS public.lead_answer_status
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result_record public.lead_answer_status;
  lead_company_id UUID;
  is_super BOOLEAN;
BEGIN
  SELECT company_id INTO lead_company_id FROM public.leads WHERE id = p_lead_id;
  IF lead_company_id IS NULL THEN
    RAISE EXCEPTION 'Lead not found or no company associated';
  END IF;

  SELECT EXISTS (
    SELECT 1 FROM public.user_roles ur
    WHERE ur.user_id = p_user_id AND ur.role = 'super_admin'
  ) INTO is_super;

  IF NOT is_super THEN
    IF NOT EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.user_id = p_user_id AND ur.company_id = lead_company_id
    ) THEN
      RAISE EXCEPTION 'User does not have access to this company';
    END IF;
  END IF;

  INSERT INTO public.lead_answer_status (
    lead_id, company_id, answer_status, attempt_number, created_by, created_at, updated_at
  ) VALUES (
    p_lead_id, lead_company_id, p_answer_status, p_attempt_number, p_user_id, NOW(), NOW()
  )
  ON CONFLICT (lead_id)
  DO UPDATE SET
    answer_status = EXCLUDED.answer_status,
    attempt_number = EXCLUDED.attempt_number,
    updated_at = NOW()
  RETURNING * INTO result_record;

  RETURN result_record;
END;
$$;

CREATE OR REPLACE FUNCTION public.reset_lead_answer_status(
  lead_uuid UUID,
  user_uuid UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  lead_company_id UUID;
  is_super BOOLEAN;
BEGIN
  SELECT company_id INTO lead_company_id FROM public.leads WHERE id = lead_uuid;
  IF lead_company_id IS NULL THEN
    RAISE EXCEPTION 'Lead not found or no company associated';
  END IF;

  SELECT EXISTS (
    SELECT 1 FROM public.user_roles ur
    WHERE ur.user_id = user_uuid AND ur.role = 'super_admin'
  ) INTO is_super;

  IF NOT is_super THEN
    IF NOT EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.user_id = user_uuid AND ur.company_id = lead_company_id
    ) THEN
      RAISE EXCEPTION 'User does not have access to this company';
    END IF;
  END IF;

  DELETE FROM public.lead_answer_status WHERE lead_id = lead_uuid;
  RETURN TRUE;
END;
$$;

-- RLS policies: allow super admins to select/modify any lead_answer_status
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'lead_answer_status' AND policyname = 'Super admins manage all answer status'
  ) THEN
    CREATE POLICY "Super admins manage all answer status" ON public.lead_answer_status
      FOR ALL USING (
        EXISTS (
          SELECT 1 FROM public.user_roles ur
          WHERE ur.user_id = auth.uid() AND ur.role = 'super_admin'
        )
      );
  END IF;
END$$;
