-- Create a default company for testing
INSERT INTO public.companies (id, name, domain, twilio_account_sid, twilio_auth_token, twilio_phone_number)
VALUES (
  gen_random_uuid(),
  'Default Company', 
  'example.com',
  NULL,
  NULL,
  NULL
) ON CONFLICT DO NOTHING;

-- Create super admin role for your user
INSERT INTO public.user_roles (user_id, role, company_id)
VALUES (
  'c21fbdc1-df7f-4daa-af44-fae26c3bb305'::uuid,
  'super_admin'::app_role,
  (SELECT id FROM public.companies WHERE name = 'Default Company' LIMIT 1)
) ON CONFLICT DO NOTHING;