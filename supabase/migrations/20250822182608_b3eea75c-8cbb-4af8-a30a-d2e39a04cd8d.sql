-- Create companies table with encrypted Twilio credentials
CREATE TABLE public.companies (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  domain TEXT UNIQUE,
  twilio_account_sid TEXT,
  twilio_auth_token TEXT,
  twilio_phone_number TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on companies
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;

-- Create user roles enum
CREATE TYPE public.app_role AS ENUM ('super_admin', 'company_admin', 'user');

-- Create user_roles table
CREATE TABLE public.user_roles (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  company_id UUID REFERENCES public.companies(id) ON DELETE CASCADE,
  role app_role NOT NULL DEFAULT 'user',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(user_id, company_id)
);

-- Enable RLS on user_roles
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- Add company_id to existing tables
ALTER TABLE public.leads ADD COLUMN company_id UUID REFERENCES public.companies(id) ON DELETE CASCADE;
ALTER TABLE public.call_logs ADD COLUMN company_id UUID REFERENCES public.companies(id) ON DELETE CASCADE;
ALTER TABLE public.lead_activities ADD COLUMN company_id UUID REFERENCES public.companies(id) ON DELETE CASCADE;

-- Create function to get user's role in company
CREATE OR REPLACE FUNCTION public.get_user_role(user_uuid UUID, company_uuid UUID)
RETURNS app_role
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
  SELECT role FROM public.user_roles 
  WHERE user_id = user_uuid AND company_id = company_uuid
  LIMIT 1;
$$;

-- Create function to get user's company
CREATE OR REPLACE FUNCTION public.get_user_company()
RETURNS UUID
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
  SELECT company_id FROM public.user_roles 
  WHERE user_id = auth.uid()
  LIMIT 1;
$$;

-- Create function to check if user is super admin
CREATE OR REPLACE FUNCTION public.is_super_admin()
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.user_roles 
    WHERE user_id = auth.uid() AND role = 'super_admin'
  );
$$;

-- RLS Policies for companies
CREATE POLICY "Super admins can view all companies" 
ON public.companies FOR SELECT 
USING (public.is_super_admin());

CREATE POLICY "Company admins can view their company" 
ON public.companies FOR SELECT 
USING (id = public.get_user_company());

CREATE POLICY "Super admins can create companies" 
ON public.companies FOR INSERT 
WITH CHECK (public.is_super_admin());

CREATE POLICY "Company admins can update their company" 
ON public.companies FOR UPDATE 
USING (id = public.get_user_company() AND public.get_user_role(auth.uid(), id) = 'company_admin');

-- RLS Policies for user_roles
CREATE POLICY "Users can view their own roles" 
ON public.user_roles FOR SELECT 
USING (user_id = auth.uid() OR public.is_super_admin());

CREATE POLICY "Super admins can manage all roles" 
ON public.user_roles FOR ALL 
USING (public.is_super_admin());

CREATE POLICY "Company admins can manage roles in their company" 
ON public.user_roles FOR ALL 
USING (company_id = public.get_user_company() AND public.get_user_role(auth.uid(), company_id) = 'company_admin');

-- Update RLS policies for existing tables to include company isolation
DROP POLICY IF EXISTS "Users can view their own leads" ON public.leads;
DROP POLICY IF EXISTS "Users can create their own leads" ON public.leads;
DROP POLICY IF EXISTS "Users can update their own leads" ON public.leads;
DROP POLICY IF EXISTS "Users can delete their own leads" ON public.leads;

CREATE POLICY "Users can view company leads" 
ON public.leads FOR SELECT 
USING (company_id = public.get_user_company());

CREATE POLICY "Users can create company leads" 
ON public.leads FOR INSERT 
WITH CHECK (company_id = public.get_user_company() AND user_id = auth.uid());

CREATE POLICY "Users can update company leads" 
ON public.leads FOR UPDATE 
USING (company_id = public.get_user_company());

CREATE POLICY "Users can delete company leads" 
ON public.leads FOR DELETE 
USING (company_id = public.get_user_company());

-- Update call_logs policies
DROP POLICY IF EXISTS "Users can view their own call logs" ON public.call_logs;
DROP POLICY IF EXISTS "Users can create their own call logs" ON public.call_logs;
DROP POLICY IF EXISTS "Users can update their own call logs" ON public.call_logs;

CREATE POLICY "Users can view company call logs" 
ON public.call_logs FOR SELECT 
USING (company_id = public.get_user_company());

CREATE POLICY "Users can create company call logs" 
ON public.call_logs FOR INSERT 
WITH CHECK (company_id = public.get_user_company() AND user_id = auth.uid());

CREATE POLICY "Users can update company call logs" 
ON public.call_logs FOR UPDATE 
USING (company_id = public.get_user_company());

-- Update lead_activities policies
DROP POLICY IF EXISTS "Users can view their own lead activities" ON public.lead_activities;
DROP POLICY IF EXISTS "Users can create their own lead activities" ON public.lead_activities;

CREATE POLICY "Users can view company lead activities" 
ON public.lead_activities FOR SELECT 
USING (company_id = public.get_user_company());

CREATE POLICY "Users can create company lead activities" 
ON public.lead_activities FOR INSERT 
WITH CHECK (company_id = public.get_user_company() AND user_id = auth.uid());

-- Create trigger for companies updated_at
CREATE TRIGGER update_companies_updated_at
BEFORE UPDATE ON public.companies
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();