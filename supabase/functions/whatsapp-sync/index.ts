import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface SyncRequest {
  companyId?: string;
  syncType: 'full' | 'recent' | 'conversations_only' | 'messages_only';
  conversationId?: string;
}

interface GreenAPIMessage {
  idMessage: string;
  timestamp: number;
  typeMessage: string;
  chatId: string;
  textMessage?: string;
  senderId: string;
  senderName: string;
}

interface GreenAPIConversation {
  id: string;
  name: string;
  lastMessage: {
    idMessage: string;
    timestamp: number;
    typeMessage: string;
    textMessage?: string;
  };
}

const formatPhoneNumber = (chatId: string): string => {
  // Remove @c.us suffix and format as international number
  const phone = chatId.replace('@c.us', '');
  return phone.startsWith('972') ? phone : `972${phone}`;
};

const convertTimestamp = (timestamp: number): string => {
  return new Date(timestamp * 1000).toISOString();
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get user from token
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);

    if (userError || !user) {
      throw new Error('Invalid user token');
    }

    // Parse request body
    const { companyId, syncType, conversationId }: SyncRequest = await req.json();

    // Get user's role and determine target company
    const { data: userRole, error: roleError } = await supabase
      .from('user_roles')
      .select('company_id, role')
      .eq('user_id', user.id)
      .single();

    if (!userRole) {
      throw new Error('User role not found');
    }

    // For super admins, use the provided companyId from the current session
    // For regular users, use their assigned company_id
    const targetCompanyId = userRole.role === 'super_admin' ? companyId : userRole.company_id;

    if (!targetCompanyId) {
      throw new Error('No company context available');
    }

    // Get company's GreenAPI credentials
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('green_api_instance_id, green_api_token')
      .eq('id', targetCompanyId)
      .single();

    if (companyError || !company) {
      throw new Error('Company not found or missing GreenAPI credentials');
    }

    if (!company.green_api_instance_id || !company.green_api_token) {
      throw new Error('GreenAPI credentials not configured for this company');
    }

    console.log(`Starting WhatsApp sync for company ${targetCompanyId}, type: ${syncType}`);

    // Extract first 4 digits for subdomain
    const instanceSubdomain = company.green_api_instance_id.substring(0, 4);
    const baseUrl = `https://${instanceSubdomain}.api.greenapi.com/waInstance${company.green_api_instance_id}`;

    let syncResults = {
      conversationsProcessed: 0,
      messagesProcessed: 0,
      errors: [] as string[]
    };

    // Sync conversations if requested
    if (syncType === 'full' || syncType === 'conversations_only') {
      try {
        // Get chats from GreenAPI
        const chatsResponse = await fetch(`${baseUrl}/getChats/${company.green_api_token}`, {
          method: 'GET',
        });

        if (!chatsResponse.ok) {
          throw new Error(`GreenAPI chats request failed: ${chatsResponse.status}`);
        }

        const chatsData = await chatsResponse.json();
        console.log(`Retrieved ${chatsData.length} chats from GreenAPI`);

        for (const chat of chatsData) {
          try {
            const phoneNumber = formatPhoneNumber(chat.id);

            // Check if conversation already exists
            const { data: existingConversation } = await supabase
              .from('whatsapp_conversations')
              .select('id, contact_name')
              .eq('company_id', targetCompanyId)
              .eq('phone_number', phoneNumber)
              .single();

            let contactName = chat.name || phoneNumber;

            // If no existing conversation, try to get contact info from GreenAPI
            if (!existingConversation) {
              try {
                const contactResponse = await fetch(
                  `${baseUrl}/getContactInfo/${company.green_api_token}`,
                  {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      chatId: chat.id
                    }),
                  }
                );

                if (contactResponse.ok) {
                  const contactData = await contactResponse.json();
                  if (contactData.name) {
                    contactName = contactData.name;
                  } else if (contactData.pushname) {
                    contactName = contactData.pushname;
                  }
                }
              } catch (contactError) {
                console.warn(`Could not fetch contact info for ${chat.id}:`, contactError);
                // Continue with chat.name or phone number
              }

              // Create new conversation
              await supabase
                .from('whatsapp_conversations')
                .insert({
                  company_id: targetCompanyId,
                  phone_number: phoneNumber,
                  green_api_chat_id: chat.id,
                  contact_name: contactName,
                  last_message: chat.lastMessage?.textMessage || null,
                  last_message_timestamp: chat.lastMessage?.timestamp ?
                    convertTimestamp(chat.lastMessage.timestamp) : null,
                  unread_count: 0
                });

              syncResults.conversationsProcessed++;
            } else if (existingConversation.contact_name === phoneNumber || !existingConversation.contact_name) {
              // Update existing conversation with better contact name if we only have phone number
              try {
                const contactResponse = await fetch(
                  `${baseUrl}/getContactInfo/${company.green_api_token}`,
                  {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      chatId: chat.id
                    }),
                  }
                );

                if (contactResponse.ok) {
                  const contactData = await contactResponse.json();
                  if (contactData.name || contactData.pushname) {
                    const betterName = contactData.name || contactData.pushname;
                    await supabase
                      .from('whatsapp_conversations')
                      .update({ contact_name: betterName })
                      .eq('id', existingConversation.id);
                  }
                }
              } catch (contactError) {
                console.warn(`Could not update contact info for ${chat.id}:`, contactError);
              }
            }
          } catch (error) {
            console.error(`Error processing chat ${chat.id}:`, error);
            syncResults.errors.push(`Chat ${chat.id}: ${error.message}`);
          }
        }
      } catch (error) {
        console.error('Error syncing conversations:', error);
        syncResults.errors.push(`Conversations sync: ${error.message}`);
      }
    }

    // Sync messages if requested
    if (syncType === 'full' || syncType === 'messages_only') {
      try {
        // Get conversations to sync messages for
        let conversationsToSync: any[];
        
        if (conversationId) {
          // Sync specific conversation
          const { data } = await supabase
            .from('whatsapp_conversations')
            .select('id, green_api_chat_id')
            .eq('id', conversationId)
            .eq('company_id', targetCompanyId);
          conversationsToSync = data || [];
        } else {
          // Sync all conversations
          const { data } = await supabase
            .from('whatsapp_conversations')
            .select('id, green_api_chat_id')
            .eq('company_id', targetCompanyId)
            .not('green_api_chat_id', 'is', null);
          conversationsToSync = data || [];
        }

        for (const conversation of conversationsToSync) {
          try {
            // Get messages from GreenAPI for this chat
            const messagesResponse = await fetch(
              `${baseUrl}/getChatHistory/${company.green_api_token}`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  chatId: conversation.green_api_chat_id,
                  count: 100 // Limit to recent 100 messages
                }),
              }
            );

            if (!messagesResponse.ok) {
              throw new Error(`GreenAPI messages request failed: ${messagesResponse.status}`);
            }

            const messagesData = await messagesResponse.json();
            console.log(`Retrieved ${messagesData.length} messages for conversation ${conversation.id}`);

            for (const message of messagesData) {
              try {
                // Check if message already exists
                const { data: existingMessage } = await supabase
                  .from('whatsapp_messages')
                  .select('id')
                  .eq('green_api_message_id', message.idMessage)
                  .single();

                if (!existingMessage && message.typeMessage === 'textMessage') {
                  // Determine sender type based on senderId
                  const senderType = message.senderId === conversation.green_api_chat_id ? 'incoming' : 'outgoing';
                  
                  await supabase
                    .from('whatsapp_messages')
                    .insert({
                      conversation_id: conversation.id,
                      green_api_message_id: message.idMessage,
                      content: message.textMessage || '',
                      message_type: 'text',
                      sender_type: senderType,
                      status: 'received',
                      created_at: convertTimestamp(message.timestamp)
                    });

                  syncResults.messagesProcessed++;
                }
              } catch (error) {
                console.error(`Error processing message ${message.idMessage}:`, error);
                syncResults.errors.push(`Message ${message.idMessage}: ${error.message}`);
              }
            }
          } catch (error) {
            console.error(`Error syncing messages for conversation ${conversation.id}:`, error);
            syncResults.errors.push(`Conversation ${conversation.id}: ${error.message}`);
          }
        }
      } catch (error) {
        console.error('Error syncing messages:', error);
        syncResults.errors.push(`Messages sync: ${error.message}`);
      }
    }

    console.log('Sync completed:', syncResults);

    return new Response(
      JSON.stringify({
        success: true,
        results: syncResults
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    console.error('Error in whatsapp-sync function:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});
