import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

function formatPhoneToE164(phone: string): string {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // If it starts with 972 (Israel country code), it's already in international format
  if (cleaned.startsWith('972')) {
    return `+${cleaned}`;
  }
  
  // If it starts with 0, replace with 972 (Israeli number)
  if (cleaned.startsWith('0')) {
    return `+972${cleaned.substring(1)}`;
  }
  
  // If it's a 9-digit number, assume it's Israeli without the leading 0
  if (cleaned.length === 9) {
    return `+972${cleaned}`;
  }
  
  // If it's a 10-digit number starting with 05, assume it's Israeli
  if (cleaned.length === 10 && cleaned.startsWith('05')) {
    return `+972${cleaned.substring(1)}`;
  }
  
  // Otherwise, assume it needs +972 prefix
  return `+972${cleaned}`;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Parse form data from Twilio
    const formData = await req.formData();
    const to = formData.get('To')?.toString();
    const from = formData.get('From')?.toString();
    const callSid = formData.get('CallSid')?.toString();

    console.log('Voice TwiML request:', { to, from, callSid });

    // If no 'To' parameter, this might be an incoming call
    if (!to) {
      const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Say voice="alice" language="he-IL">שלום, זה מערכת הטלפונים של המשרד. אנא המתן.</Say>
  <Pause length="1"/>
  <Hangup/>
</Response>`;

      return new Response(twiml, {
        headers: {
          ...corsHeaders,
          'Content-Type': 'text/xml',
        },
      });
    }

    // Format the phone number for outgoing calls
    const formattedTo = formatPhoneToE164(to);
    console.log(`Formatted phone number: ${to} -> ${formattedTo}`);

    // Create TwiML for outgoing call
    const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Dial timeout="30" record="record-from-answer" recordingStatusCallback="${Deno.env.get('SUPABASE_URL')}/functions/v1/twilio-webhook">
    <Number>${formattedTo}</Number>
  </Dial>
</Response>`;

    return new Response(twiml, {
      headers: {
        ...corsHeaders,
        'Content-Type': 'text/xml',
      },
    });

  } catch (error) {
    console.error('Error generating TwiML:', error);
    
    // Return error TwiML
    const errorTwiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Say voice="alice" language="he-IL">מצטערים, אירעה שגיאה. אנא נסה שוב מאוחר יותר.</Say>
  <Hangup/>
</Response>`;

    return new Response(errorTwiml, {
      headers: {
        ...corsHeaders,
        'Content-Type': 'text/xml',
      },
    });
  }
});
