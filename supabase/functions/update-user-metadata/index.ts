import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface UpdateUserMetadataRequest {
  userId: string;
  updates: {
    full_name?: string;
    email?: string;
  };
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { userId, updates }: UpdateUserMetadataRequest = await req.json();

    if (!userId) {
      return new Response(
        JSON.stringify({ success: false, error: 'userId is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    if (!updates || Object.keys(updates).length === 0) {
      return new Response(
        JSON.stringify({ success: false, error: 'updates object is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('Updating user metadata for:', userId, 'with updates:', updates);

    // Prepare the update object for auth.users
    const authUpdates: any = {};

    // Update email if provided
    if (updates.email) {
      authUpdates.email = updates.email;
    }

    // Update user metadata if full_name is provided
    if (updates.full_name) {
      // First get current user metadata
      const { data: currentUser, error: getUserError } = await supabaseAdmin.auth.admin.getUserById(userId);
      
      if (getUserError) {
        throw new Error('Failed to get current user: ' + getUserError.message);
      }

      // Merge with existing metadata
      authUpdates.user_metadata = {
        ...currentUser.user?.user_metadata,
        full_name: updates.full_name
      };
    }

    // Update the user in auth
    const { data: updatedUser, error: updateError } = await supabaseAdmin.auth.admin.updateUserById(userId, authUpdates);

    if (updateError) {
      console.error('Error updating user:', updateError);
      throw new Error('Failed to update user: ' + updateError.message);
    }

    console.log('Successfully updated user metadata for:', userId);

    return new Response(
      JSON.stringify({
        success: true,
        user: {
          id: updatedUser.user?.id,
          email: updatedUser.user?.email,
          full_name: updatedUser.user?.user_metadata?.full_name
        }
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error: any) {
    console.error('Error in update-user-metadata:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Internal server error'
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
};

serve(handler);
