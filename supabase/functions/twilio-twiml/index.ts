import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Parse form data from Twilio
    const formData = await req.formData();
    const to = formData.get('To')?.toString();
    const from = formData.get('From')?.toString();
    const callSid = formData.get('CallSid')?.toString();
    const leadId = formData.get('leadId')?.toString();

    console.log('TwiML request received:', {
      to,
      from,
      callSid,
      leadId
    });

    // Create Supabase client for logging
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Log the call attempt
    if (leadId && callSid) {
      try {
        await supabase
          .from('call_logs')
          .insert({
            lead_id: leadId,
            twilio_call_sid: callSid,
            phone_number: to,
            direction: 'outbound',
            status: 'initiated'
          });

        await supabase
          .from('lead_activities')
          .insert({
            lead_id: leadId,
            activity_type: 'call',
            description: `Outbound call initiated to ${to}`,
            metadata: { call_sid: callSid, status: 'initiated' }
          });
      } catch (error) {
        console.error('Failed to log call:', error);
      }
    }

    // Generate TwiML response for Voice SDK
    const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Dial callerId="${from}" record="record-from-answer" recordingStatusCallback="${supabaseUrl}/functions/v1/twilio-webhook">
    <Number>${to}</Number>
  </Dial>
</Response>`;

    return new Response(twiml, {
      headers: { 
        ...corsHeaders, 
        'Content-Type': 'text/xml' 
      },
    });

  } catch (error) {
    console.error('Error in twilio-twiml function:', error);
    
    // Return error TwiML
    const errorTwiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Say voice="alice">Sorry, there was an error processing your call. Please try again later.</Say>
  <Hangup/>
</Response>`;

    return new Response(errorTwiml, {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'text/xml' },
    });
  }
});
