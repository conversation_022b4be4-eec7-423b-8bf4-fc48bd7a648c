import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface CreateCompanyAdminRequest {
  companyId: string;
  adminEmail: string;
  adminFirstName: string;
  adminLastName: string;
  adminPhone: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { companyId, adminEmail, adminFirstName, adminLastName, adminPhone }: CreateCompanyAdminRequest = await req.json();

    if (!companyId || !adminEmail || !adminFirstName || !adminLastName) {
      return new Response(
        JSON.stringify({ success: false, error: 'Missing required fields' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('Creating company admin for company:', companyId);

    // Generate a temporary password
    const tempPassword = `Admin${Math.random().toString(36).slice(-8)}!`;
    const fullName = `${adminFirstName} ${adminLastName}`;

    let userId: string;

    // Check if user already exists
    const { data: existingUsers, error: listError } = await supabaseAdmin.auth.admin.listUsers();
    if (listError) {
      throw new Error('Failed to check existing users: ' + listError.message);
    }

    const existingUser = existingUsers.users.find(u => u.email === adminEmail);

    if (existingUser) {
      // User exists, update their details and role
      userId = existingUser.id;
      console.log('Found existing user:', userId);

      // Update existing user's password and metadata
      const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(userId, {
        password: tempPassword,
        user_metadata: {
          full_name: fullName,
          first_name: adminFirstName,
          last_name: adminLastName,
          phone: adminPhone
        }
      });

      if (updateError) {
        throw new Error('Failed to update existing user: ' + updateError.message);
      }

      // Check if user already has a role in this company
      const { data: existingRole, error: roleCheckError } = await supabaseAdmin
        .from('user_roles')
        .select('*')
        .eq('user_id', userId)
        .eq('company_id', companyId)
        .single();

      if (!roleCheckError && existingRole) {
        // Update existing role to company_admin
        const { error: roleUpdateError } = await supabaseAdmin
          .from('user_roles')
          .update({ role: 'company_admin' })
          .eq('user_id', userId)
          .eq('company_id', companyId);

        if (roleUpdateError) {
          throw new Error('Failed to update user role: ' + roleUpdateError.message);
        }
      } else {
        // Create new role entry
        const { error: roleError } = await supabaseAdmin
          .from('user_roles')
          .insert({
            user_id: userId,
            company_id: companyId,
            role: 'company_admin'
          });

        if (roleError) {
          throw new Error('Failed to create user role: ' + roleError.message);
        }
      }
    } else {
      // Create new user
      console.log('Creating new user with email:', adminEmail);
      
      const { data: newUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
        email: adminEmail,
        password: tempPassword,
        user_metadata: {
          full_name: fullName,
          first_name: adminFirstName,
          last_name: adminLastName,
          phone: adminPhone
        },
        email_confirm: true // Auto-confirm email
      });

      if (createError) {
        throw new Error('Failed to create user: ' + createError.message);
      }

      if (!newUser.user) {
        throw new Error('User creation failed - no user returned');
      }

      userId = newUser.user.id;

      // Create user role entry
      const { error: roleError } = await supabaseAdmin
        .from('user_roles')
        .insert({
          user_id: userId,
          company_id: companyId,
          role: 'company_admin'
        });

      if (roleError) {
        throw new Error('Failed to create user role: ' + roleError.message);
      }
    }

    console.log('Successfully created/updated company admin:', userId);

    return new Response(
      JSON.stringify({
        success: true,
        userId: userId,
        message: `Company admin created successfully! Temporary password: ${tempPassword}`
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error: any) {
    console.error('Error in create-company-admin:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Internal server error'
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
};

serve(handler);
