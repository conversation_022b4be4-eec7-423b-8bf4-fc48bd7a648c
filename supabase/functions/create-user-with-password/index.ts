import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface CreateUserRequest {
  email: string;
  password: string;
  fullName: string;
  role: 'company_admin' | 'user'; // Frontend sends 'company_admin' or 'user'
  companyId?: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log('=== FUNCTION START ===');
    console.log('Method:', req.method);
    console.log('Headers:', Object.fromEntries(req.headers.entries()));

    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    console.log('About to parse JSON body...');
    const body: CreateUserRequest = await req.json();
    console.log('Received body:', JSON.stringify(body, null, 2));

    const { email, password, fullName, role, companyId } = body;
    console.log('Extracted fields:', { email, password: '***', fullName, role, companyId });

    if (!email || !password || !fullName || !role) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Missing required fields'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Check if user already exists and handle appropriately
    console.log('Creating user in Supabase Auth...');
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        full_name: fullName
      }
    });

    let userId: string;

    if (authError) {
      console.error('Auth creation error:', authError);

      // If user already exists, try to get their ID and continue with role creation
      if (authError.message.includes('already been registered')) {
        console.log('User already exists, attempting to find existing user...');

        // Get the existing user by email
        const { data: existingUsers, error: listError } = await supabaseAdmin.auth.admin.listUsers();

        if (listError) {
          console.error('Error listing users:', listError);
          return new Response(
            JSON.stringify({
              success: false,
              error: 'Failed to check existing users: ' + listError.message
            }),
            {
              status: 400,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          );
        }

        const existingUser = existingUsers.users.find(u => u.email === email);

        if (!existingUser) {
          return new Response(
            JSON.stringify({
              success: false,
              error: 'User exists but could not be found'
            }),
            {
              status: 400,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          );
        }

        userId = existingUser.id;
        console.log('Found existing user:', userId);

        // Update existing user's password and metadata
        console.log('Updating existing user password and metadata...');
        const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(userId, {
          password: password,
          user_metadata: {
            full_name: fullName
          }
        });

        if (updateError) {
          console.error('Error updating existing user:', updateError);
          // Continue anyway - the role creation is more important
        } else {
          console.log('Successfully updated existing user data');
        }

        // Check if they already have a role in this company
        const { data: existingRole, error: roleCheckError } = await supabaseAdmin
          .from('user_roles')
          .select('*')
          .eq('user_id', userId)
          .eq('company_id', companyId)
          .single();

        if (!roleCheckError && existingRole) {
          // Update existing role instead of creating new one
          console.log('User already has role in company, updating role...');
          const { error: roleUpdateError } = await supabaseAdmin
            .from('user_roles')
            .update({
              role: role === 'company_admin' ? 'company_admin' : 'user'
            })
            .eq('user_id', userId)
            .eq('company_id', companyId);

          if (roleUpdateError) {
            console.error('Error updating user role:', roleUpdateError);
            return new Response(
              JSON.stringify({
                success: false,
                error: 'Failed to update user role: ' + roleUpdateError.message
              }),
              {
                status: 400,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
              }
            );
          }

          return new Response(
            JSON.stringify({
              success: true,
              userId: userId,
              message: 'User updated successfully (password, name, and role)'
            }),
            {
              status: 200,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          );
        }

        console.log('User exists but no role in this company, proceeding with role creation...');
      } else {
        // Other auth errors
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Auth error: ' + authError.message
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      }
    } else {
      userId = authData.user!.id;
      console.log('Auth user created successfully:', userId);
    }

    // Create user role entry (this links the auth user to a company with a role)
    console.log('Creating user role entry...');
    console.log('Role data:', {
      user_id: userId,
      company_id: companyId,
      role: role === 'company_admin' ? 'company_admin' : 'user'
    });

    const { error: roleError } = await supabaseAdmin
      .from('user_roles')
      .insert({
        user_id: userId,
        company_id: companyId,
        role: role === 'company_admin' ? 'company_admin' : 'user'
      });

    if (roleError) {
      console.error('Role creation error:', roleError);
      console.error('Role error details:', JSON.stringify(roleError, null, 2));

      // Only clean up auth user if we created them in this request
      if (authData?.user) {
        console.log('Cleaning up newly created auth user...');
        await supabaseAdmin.auth.admin.deleteUser(userId);
      }

      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to create user role: ' + roleError.message,
          details: roleError
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    console.log('User role created successfully!');

    return new Response(
      JSON.stringify({
        success: true,
        userId: userId,
        message: 'User created successfully'
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('=== FUNCTION ERROR ===');
    console.error('Error type:', error.constructor.name);
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Internal server error'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
};

serve(handler);
