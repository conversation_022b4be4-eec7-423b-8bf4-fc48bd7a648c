import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    console.log('Workflow scheduler running...');

    // Process new workflow triggers
    await processWorkflowTriggers(supabaseAdmin);

    // Process pending executions (wait steps that have expired)
    await processPendingExecutions(supabaseAdmin);

    // Retry failed executions (with exponential backoff)
    await retryFailedExecutions(supabaseAdmin);

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Workflow scheduler completed successfully',
        timestamp: new Date().toISOString()
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Error in workflow-scheduler:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Internal server error' 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
};

async function processWorkflowTriggers(supabaseAdmin: any) {
  // Get unprocessed workflow triggers
  const { data: triggers, error } = await supabaseAdmin
    .from('workflow_triggers')
    .select('*')
    .eq('processed', false)
    .order('created_at', { ascending: true })
    .limit(50); // Process in batches

  if (error) {
    console.error('Error fetching workflow triggers:', error);
    return;
  }

  console.log(`Processing ${triggers.length} workflow triggers`);

  for (const trigger of triggers) {
    try {
      // Call workflow executor to trigger workflows
      const response = await fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/workflow-executor`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`
        },
        body: JSON.stringify({
          action: 'trigger',
          triggerData: {
            entity_type: trigger.entity_type,
            entity_id: trigger.entity_id,
            old_status: trigger.old_status,
            new_status: trigger.new_status,
            company_id: trigger.company_id
          }
        })
      });

      if (response.ok) {
        // Mark trigger as processed
        await supabaseAdmin
          .from('workflow_triggers')
          .update({ processed: true })
          .eq('id', trigger.id);

        console.log(`Processed trigger ${trigger.id} for ${trigger.entity_type} ${trigger.entity_id}`);
      } else {
        console.error(`Failed to process trigger ${trigger.id}: ${response.statusText}`);
      }
    } catch (error) {
      console.error(`Error processing trigger ${trigger.id}:`, error);
    }
  }
}

async function processPendingExecutions(supabaseAdmin: any) {
  // Find executions that are ready to continue (wait steps that have expired)
  const { data: pendingExecutions, error } = await supabaseAdmin
    .from('workflow_executions')
    .select(`
      *,
      workflows!inner (
        id,
        workflow_steps (
          id,
          step_order,
          step_type,
          step_config
        )
      )
    `)
    .eq('status', 'running')
    .not('next_execution_at', 'is', null)
    .lte('next_execution_at', new Date().toISOString());

  if (error) {
    console.error('Error fetching pending executions:', error);
    return;
  }

  console.log(`Processing ${pendingExecutions.length} pending executions`);

  for (const execution of pendingExecutions) {
    try {
      // Call workflow executor to continue execution
      const response = await fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/workflow-executor`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`
        },
        body: JSON.stringify({
          action: 'process_pending'
        })
      });

      if (!response.ok) {
        console.error(`Failed to process execution ${execution.id}: ${response.statusText}`);
      }
    } catch (error) {
      console.error(`Error processing execution ${execution.id}:`, error);
    }
  }
}

async function retryFailedExecutions(supabaseAdmin: any) {
  // Find failed executions that should be retried (with exponential backoff)
  const { data: failedExecutions, error } = await supabaseAdmin
    .from('workflow_executions')
    .select('*')
    .eq('status', 'failed')
    .lt('retry_count', 5) // Max 5 retries
    .lt('updated_at', new Date(Date.now() - Math.pow(2, 1) * 60 * 1000).toISOString()); // Wait at least 2 minutes

  if (error) {
    console.error('Error fetching failed executions:', error);
    return;
  }

  console.log(`Retrying ${failedExecutions.length} failed executions`);

  for (const execution of failedExecutions) {
    try {
      // Calculate backoff time based on retry count (exponential backoff)
      const backoffMinutes = Math.pow(2, execution.retry_count);
      const lastRetry = new Date(execution.updated_at);
      const nextRetry = new Date(lastRetry.getTime() + backoffMinutes * 60 * 1000);
      
      if (new Date() >= nextRetry) {
        // Call workflow executor to retry execution
        const response = await fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/workflow-executor`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`
          },
          body: JSON.stringify({
            action: 'retry_failed',
            executionId: execution.id
          })
        });

        if (!response.ok) {
          console.error(`Failed to retry execution ${execution.id}: ${response.statusText}`);
        }
      }
    } catch (error) {
      console.error(`Error retrying execution ${execution.id}:`, error);
    }
  }
}

serve(handler);
