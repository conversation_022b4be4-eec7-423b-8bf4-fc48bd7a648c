import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface TwilioWebhookData {
  CallSid: string;
  AccountSid: string;
  From: string;
  To: string;
  CallStatus: string;
  Direction: string;
  Duration?: string;
  CallDuration?: string;
  RecordingUrl?: string;
  RecordingSid?: string;
  RecordingDuration?: string;
  Timestamp?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Parse form data from Twilio webhook
    const formData = await req.formData();
    const webhookData: Partial<TwilioWebhookData> = {};

    for (const [key, value] of formData.entries()) {
      webhookData[key as keyof TwilioWebhookData] = value.toString();
    }

    console.log('Twilio webhook received:', webhookData);

    const {
      CallSid: callSid,
      AccountSid: accountSid,
      CallStatus: callStatus,
      CallDuration: callDuration,
      Duration: duration,
      From: from,
      To: to,
      Direction: direction,
      RecordingUrl: recordingUrl,
      RecordingSid: recordingSid
    } = webhookData;

    if (!callSid || !callStatus) {
      throw new Error('Missing required webhook parameters');
    }

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Try to find existing call log by call_sid or twilio_call_sid
    let { data: callLog, error: findError } = await supabase
      .from('call_logs')
      .select('*')
      .or(`call_sid.eq.${callSid},twilio_call_sid.eq.${callSid}`)
      .single();

    if (findError && findError.code !== 'PGRST116') {
      console.error('Error finding call log:', findError);
    }

    // Prepare update data
    const updateData: any = {
      status: callStatus,
      call_sid: callSid,
      twilio_call_sid: callSid, // For backward compatibility
    };

    // Use CallDuration first, then Duration as fallback
    const callDurationValue = callDuration || duration;
    if (callDurationValue) {
      updateData.duration = parseInt(callDurationValue);
    }

    if (callStatus === 'completed' || callStatus === 'failed' || callStatus === 'canceled') {
      updateData.ended_at = new Date().toISOString();
    }

    // Add recording information if available
    if (recordingUrl) {
      updateData.recording_url = recordingUrl;
    }
    if (recordingSid) {
      updateData.recording_sid = recordingSid;
    }

    // Add webhook metadata
    updateData.webhook_data = webhookData;

    if (callLog) {
      // Update existing call log
      const { error: updateError } = await supabase
        .from('call_logs')
        .update(updateData)
        .eq('id', callLog.id);

      if (updateError) {
        console.error('Failed to update call log:', updateError);
      } else {
        console.log('Updated call log for CallSid:', callSid);
      }
    } else {
      // If no existing call log found, try to find company by account SID and create one
      if (accountSid) {
        const { data: company } = await supabase
          .from('companies')
          .select('id')
          .eq('twilio_account_sid', accountSid)
          .single();

        if (company) {
          const { error: insertError } = await supabase
            .from('call_logs')
            .insert({
              ...updateData,
              company_id: company.id,
              from_number: from,
              to_number: to,
              direction: direction?.toLowerCase() || 'unknown',
              created_at: new Date().toISOString()
            });

          if (insertError) {
            console.error('Failed to create call log:', insertError);
          } else {
            console.log('Created call log for CallSid:', callSid);
          }
        }
      }
    }

    // Add activity log for status change if we have a call log with lead info
    if (callLog && callLog.lead_id) {
      await supabase
        .from('lead_activities')
        .insert({
          user_id: callLog.user_id,
          lead_id: callLog.lead_id,
          activity_type: 'call',
          description: `Call ${callStatus}${callDurationValue ? ` (${callDurationValue}s)` : ''}`,
          metadata: {
            call_sid: callSid,
            status: callStatus,
            duration: callDurationValue ? parseInt(callDurationValue) : null,
            recording_url: recordingUrl
          },
          company_id: callLog.company_id
        });
    }

    return new Response('OK', {
      headers: { ...corsHeaders, 'Content-Type': 'text/plain' },
    });

  } catch (error) {
    console.error('Error in twilio-webhook function:', error);
    return new Response('Internal Server Error', {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'text/plain' },
    });
  }
});