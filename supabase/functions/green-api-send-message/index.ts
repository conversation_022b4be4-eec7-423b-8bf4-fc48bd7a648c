import { createClient } from 'npm:@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

interface SendMessageRequest {
  phoneNumber: string;
  message: string;
  leadId?: string;
  companyId?: string; // For super admin users
}

const handler = async (req: Request): Promise<Response> => {
  console.log(`[${new Date().toISOString()}] ${req.method} request received`);

  // Handle CORS preflight requests immediately, before any other processing
  if (req.method === 'OPTIONS') {
    console.log('Handling CORS preflight request');
    return new Response(null, {
      status: 200,
      headers: corsHeaders
    });
  }

  // Only proceed with authentication for non-OPTIONS requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('Authorization header missing');
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token);

    console.log('Authentication result:', { hasUser: !!user, authError });

    if (!user) {
      throw new Error('Unauthorized');
    }

    const { phoneNumber, message, leadId, companyId }: SendMessageRequest = await req.json();
    console.log('Request payload:', { phoneNumber: phoneNumber?.substring(0, 5) + '***', message: message?.substring(0, 20) + '...', leadId, companyId });

    // Get user's role and determine target company
    const { data: userRole, error: roleError } = await supabaseClient
      .from('user_roles')
      .select('company_id, role')
      .eq('user_id', user.id)
      .single();

    console.log('User role query result:', { userRole, roleError });

    if (!userRole) {
      throw new Error('User role not found');
    }

    // For super admins, use the provided companyId from the current session
    // For regular users, use their assigned company_id
    const targetCompanyId = userRole.role === 'super_admin' ? companyId : userRole.company_id;
    console.log('Target company determination:', { userRole: userRole.role, providedCompanyId: companyId, targetCompanyId });

    if (!targetCompanyId) {
      throw new Error('No company context available');
    }

    const { data: company, error: companyError } = await supabaseClient
      .from('companies')
      .select('green_api_instance_id, green_api_token')
      .eq('id', targetCompanyId)
      .single();

    console.log('Company query result:', {
      company: company ? {
        hasInstanceId: !!company.green_api_instance_id,
        hasToken: !!company.green_api_token
      } : null,
      companyError
    });

    if (!company?.green_api_instance_id || !company?.green_api_token) {
      throw new Error('GreenAPI credentials not configured for company');
    }

    // Format phone number (remove any non-digits and ensure international format)
    const formattedPhone = phoneNumber.replace(/\D/g, '');
    const chatId = formattedPhone.startsWith('972') ? formattedPhone : `972${formattedPhone.substring(1)}`;

    // Send message via GreenAPI - Extract first 4 digits for subdomain
    const instanceSubdomain = company.green_api_instance_id.substring(0, 4);
    const greenApiUrl = `https://${instanceSubdomain}.api.greenapi.com/waInstance${company.green_api_instance_id}/sendMessage/${company.green_api_token}`;
    
    const greenApiResponse = await fetch(greenApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chatId: `${chatId}@c.us`,
        message: message,
      }),
    });

    const greenApiResult = await greenApiResponse.json();
    console.log('GreenAPI response:', greenApiResult);

    if (!greenApiResponse.ok) {
      throw new Error(`GreenAPI error: ${greenApiResult.error || 'Unknown error'}`);
    }

    // Find or create conversation
    let { data: conversation } = await supabaseClient
      .from('whatsapp_conversations')
      .select('id')
      .eq('company_id', targetCompanyId)
      .eq('phone_number', formattedPhone)
      .single();

    if (!conversation) {
      // Get lead name if leadId provided
      let contactName = formattedPhone;
      if (leadId) {
        const { data: lead } = await supabaseClient
          .from('leads')
          .select('full_name')
          .eq('id', leadId)
          .single();
        
        if (lead) {
          contactName = lead.full_name;
        }
      }

      const { data: newConversation } = await supabaseClient
        .from('whatsapp_conversations')
        .insert({
          company_id: targetCompanyId,
          lead_id: leadId || null,
          phone_number: formattedPhone,
          green_api_chat_id: `${chatId}@c.us`,
          contact_name: contactName,
          last_message: message,
          last_message_timestamp: new Date().toISOString(),
        })
        .select('id')
        .single();

      conversation = newConversation;
    } else {
      // Update conversation with last message
      await supabaseClient
        .from('whatsapp_conversations')
        .update({
          last_message: message,
          last_message_timestamp: new Date().toISOString(),
        })
        .eq('id', conversation.id);
    }

    // Store the message
    await supabaseClient
      .from('whatsapp_messages')
      .insert({
        conversation_id: conversation!.id,
        green_api_message_id: greenApiResult.idMessage,
        content: message,
        message_type: 'text',
        sender_type: 'outgoing',
        status: 'sent',
      });

    return new Response(
      JSON.stringify({ 
        success: true, 
        messageId: greenApiResult.idMessage,
        conversationId: conversation!.id 
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error in green-api-send-message:`, error);

    // Determine appropriate status code
    const status = error.message?.includes('Unauthorized') ? 401 :
                   error.message?.includes('not found') ? 404 :
                   error.message?.includes('not configured') ? 400 :
                   error.message?.includes('No company context') ? 400 : 500;

    return new Response(
      JSON.stringify({
        error: error.message,
        timestamp: new Date().toISOString()
      }),
      {
        status,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
};

Deno.serve(handler);