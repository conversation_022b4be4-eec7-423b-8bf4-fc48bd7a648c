# AI Agent Integration Guide

## Overview

This document outlines the API infrastructure for integrating an AI chat agent with the Legal Hebrew Nexus application. The AI agent can interact with the system via WhatsApp to manage leads, cases, and time tracking.

## Authentication

The AI agent uses API key authentication:

```
Headers:
x-api-key: your-ai-agent-api-key
Content-Type: application/json
```

## Available API Endpoints

### Lead Management

#### Create Lead
```http
POST /api/leads
Content-Type: application/json
x-api-key: your-api-key

{
  "full_name": "שם הלקוח",
  "phone": "0501234567",
  "email": "<EMAIL>",
  "source": "WhatsApp AI Agent",
  "status": "ליד חדש",
  "value": 10000,
  "notes": "הערות נוספות"
}
```

#### Update Lead Status
```http
PUT /api/leads/{leadId}/status
Content-Type: application/json
x-api-key: your-api-key

{
  "status": "צריך פולואפ",
  "notes": "הלקוח מעוניין בפגישה"
}
```

#### Search Leads
```http
GET /api/leads/search?q=search_term
x-api-key: your-api-key
```

### Case Management

#### Create Case
```http
POST /api/cases
Content-Type: application/json
x-api-key: your-api-key

{
  "title": "תיק דיני משפחה",
  "description": "תיאור התיק",
  "case_type_id": "uuid-of-case-type",
  "status": "בקליטה",
  "value": 50000,
  "lead_id": "uuid-of-lead",
  "deadline": "2024-12-31T23:59:59Z"
}
```

#### Update Case Status
```http
PUT /api/cases/{caseId}/status
Content-Type: application/json
x-api-key: your-api-key

{
  "status": "פתוח",
  "notes": "התיק עבר לטיפול פעיל"
}
```

#### Add Time Entry
```http
POST /api/cases/{caseId}/time-entries
Content-Type: application/json
x-api-key: your-api-key

{
  "description": "ייעוץ טלפוני עם הלקוח",
  "duration": 30,
  "hourly_rate": 500,
  "start_time": "2024-01-15T10:00:00Z",
  "end_time": "2024-01-15T10:30:00Z"
}
```

#### Search Cases
```http
GET /api/cases/search?q=search_term
x-api-key: your-api-key
```

#### Get Case Types
```http
GET /api/case-types
x-api-key: your-api-key
```

### WhatsApp Integration

#### Log WhatsApp Activity
```http
POST /api/whatsapp/activity
Content-Type: application/json
x-api-key: your-api-key

{
  "phone_number": "0501234567",
  "message_type": "incoming",
  "message_content": "שלום, אני מעוניין בייעוץ משפטי",
  "lead_id": "optional-lead-uuid"
}
```

## AI Agent Helper Functions

The system provides pre-built helper functions for common AI agent operations:

```typescript
import { createAIAgentHelpers } from './lib/api-routes';

const aiHelpers = createAIAgentHelpers();

// Create lead from WhatsApp
await aiHelpers.createLeadFromWhatsApp(
  "0501234567", 
  "יוסי כהן", 
  "שלום, אני צריך עורך דין"
);

// Create case from conversation
await aiHelpers.createCaseFromConversation(
  "תיק גירושין - יוסי כהן",
  "הלקוח מעוניין בהליך גירושין",
  "lead-uuid"
);

// Update case status
await aiHelpers.updateCaseStatus(
  "case-uuid",
  "פתוח",
  "התיק אושר ועבר לטיפול"
);

// Add time entry
await aiHelpers.addTimeEntry(
  "case-uuid",
  "שיחת ייעוץ ראשונית",
  45, // minutes
  500 // hourly rate
);

// Log WhatsApp message
await aiHelpers.logWhatsAppMessage(
  "0501234567",
  "incoming",
  "תודה על הייעוץ",
  "lead-uuid"
);
```

## Status Values

### Lead Statuses (Hebrew)
- `ליד חדש` - New Lead
- `צריך פולואפ` - Needs Follow-up
- `לקוח סגור` - Closed Client
- `לא ענה` - No Answer
- `לא מעוניין` - Not Interested
- `לא מתאים` - Not Suitable

### Case Statuses (Hebrew)
- `בקליטה` - Intake
- `פתוח` - Open
- `סגור` - Closed

## Common Case Types (Hebrew)
- `דיני משפחה` - Family Law
- `דיני חוזים` - Contract Law
- `דיני נזיקין` - Tort Law
- `דיני נדל"ן` - Real Estate Law
- `דיני עבודה` - Labor Law
- `דיני מסחר` - Commercial Law
- `דיני פלילי` - Criminal Law

## Error Handling

All API responses follow this format:

```json
{
  "success": true,
  "data": { ... }
}
```

Or for errors:

```json
{
  "success": false,
  "error": "Error message"
}
```

HTTP Status Codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `404` - Not Found
- `500` - Internal Server Error

## AI Agent Workflow Examples

### 1. New Client Inquiry via WhatsApp

```typescript
// 1. Log incoming message
await aiHelpers.logWhatsAppMessage(
  "0501234567",
  "incoming",
  "שלום, אני צריך עורך דין לתיק גירושין"
);

// 2. Create lead
const leadResult = await aiHelpers.createLeadFromWhatsApp(
  "0501234567",
  "לקוח חדש מוואטסאפ",
  "מעוניין בתיק גירושין"
);

// 3. Send response and log it
await aiHelpers.logWhatsAppMessage(
  "0501234567",
  "outgoing",
  "שלום! אשמח לעזור לך. אני אעביר את פרטיך לעורך הדין המתאים."
);
```

### 2. Case Creation and Time Tracking

```typescript
// 1. Create case for existing lead
const caseResult = await aiHelpers.createCaseFromConversation(
  "תיק גירושין - יוסי כהן",
  "הלקוח מעוניין בהליך גירושין מוסכם",
  "existing-lead-uuid"
);

// 2. Log consultation time
await aiHelpers.addTimeEntry(
  caseResult.data.id,
  "ייעוץ ראשוני בוואטסאפ",
  15, // 15 minutes
  400 // hourly rate
);

// 3. Update case status
await aiHelpers.updateCaseStatus(
  caseResult.data.id,
  "פתוח",
  "התיק נפתח לאחר ייעוץ ראשוני"
);
```

## Security Considerations

1. **API Key Management**: Store API keys securely and rotate them regularly
2. **Rate Limiting**: Implement rate limiting to prevent abuse
3. **Input Validation**: All inputs are validated and sanitized
4. **Company Isolation**: All data is isolated by company_id
5. **Audit Trail**: All actions are logged for audit purposes

## Database Schema

The system uses the following main tables:
- `leads` - Lead information
- `cases` - Case management
- `case_time_entries` - Time tracking
- `lead_activities` - Activity logs
- `case_types` - Legal case categories
- `companies` - Multi-tenant support
- `user_roles` - User permissions

## Next Steps

1. Set up webhook endpoints for real-time WhatsApp integration
2. Implement AI agent authentication system
3. Add more sophisticated search and filtering capabilities
4. Create dashboard for AI agent activity monitoring
5. Add support for file attachments and document management
